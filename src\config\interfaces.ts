/**
 * Interface định nghĩa cấu trúc của cấu hình <PERSON>ng dụng
 */
export interface AppConfig {
  // Server
  port: number;
  nodeEnv: string;
  apiPrefix: string;

  // Database
  database: DatabaseConfig;

  // Storage
  storage: StorageConfig;

  // Authentication
  auth: AuthConfig;

  // External Services
  services: ServicesConfig;

  // S3
  s3: S3Config;
}

/**
 * Interface định nghĩa cấu trúc của cấu hình database
 */
export interface DatabaseConfig {
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  ssl: boolean;
}

/**
 * Interface định nghĩa cấu trúc của cấu hình storage
 */
export interface StorageConfig {
  cloudflare: {
    region: string;
    accessKey: string;
    secretKey: string;
    endpoint: string;
    bucketName: string;
  };
  cdn: {
    url: string;
    secretKey: string;
  };
}

/**
 * Interface định nghĩa cấu trúc của cấu hình authentication
 */
export interface AuthConfig {
  jwt: {
    secret: string;
    expirationTime: string;
    refreshSecret?: string;
    refreshExpirationTime?: string;
  };
}

/**
 * Interface định nghĩa cấu trúc của cấu hình external services
 */
export interface ServicesConfig {
  openai: {
    apiKey: string;
    organizationId?: string;
  };
  anthropic?: {
    apiKey: string;
  };
  googleAI?: {
    apiKey: string;
  };
  deepseek?: {
    apiKey: string;
  };
  metaAI?: {
    apiKey: string;
  };
  redis?: {
    url: string;
    password?: string;
  };
  email?: {
    apiUrl?: string;
    smtpHost?: string;
    smtpPort?: number;
    smtpUser?: string;
    smtpPass?: string;
  };
}

export interface S3Config {
  s3?:{
    endpoint: string;
    accessKey: string;
    secretAccessKey: string;
    region: string;
    bucketName: string;
  },
  cdn?:{
    url: string;
    secretKey: string;
  }
}