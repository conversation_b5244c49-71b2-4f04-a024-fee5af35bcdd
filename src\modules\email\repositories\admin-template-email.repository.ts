import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdminTemplateEmail } from '../entities/admin-template-email.entity';
import { AppException } from '@/common/exceptions/app.exception';
import { ErrorCode } from '@/common/exceptions/app.exception';

/**
 * Repository cho AdminTemplateEmail
 */
@Injectable()
export class AdminTemplateEmailRepository {
  private readonly logger = new Logger(AdminTemplateEmailRepository.name);

  constructor(
    @InjectRepository(AdminTemplateEmail)
    private readonly repository: Repository<AdminTemplateEmail>,
  ) {}

  /**
   * Tìm template email theo ID
   * @param id ID của template
   * @returns Template email hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<AdminTemplateEmail | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm template email theo danh mục
   * @param category Danh mục của template
   * @returns Template email hoặc null nếu không tìm thấy
   */
  async findByCategory(category: string): Promise<AdminTemplateEmail | null> {
    return this.repository.findOne({ where: { category } });
  }

  /**
   * Lấy danh sách tất cả template email
   * @returns Danh sách template email
   */
  async findAll(): Promise<AdminTemplateEmail[]> {
    return this.repository.find();
  }

  /**
   * Lấy danh sách tất cả template email không bao gồm nội dung
   * @returns Danh sách template email không bao gồm nội dung
   */
  async findAllWithoutContent(): Promise<Partial<AdminTemplateEmail>[]> {
    return this.repository.find({
      select: ['id', 'name', 'subject', 'category', 'createdAt', 'updatedAt', 'createdBy', 'updatedBy']
    });
  }

  /**
   * Tạo mới template email
   * @param data Dữ liệu template email
   * @returns Template email đã tạo
   */
  async create(data: Partial<AdminTemplateEmail>): Promise<AdminTemplateEmail> {
    try {
      const template = this.repository.create({
        ...data,
        createdAt: Date.now(),
      });
      return this.repository.save(template);
    } catch (error) {
      this.logger.error(`Error creating email template: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể tạo mẫu email',
      );
    }
  }

  /**
   * Cập nhật template email
   * @param id ID của template cần cập nhật
   * @param data Dữ liệu cần cập nhật
   * @returns Template email đã cập nhật
   */
  async update(id: number, data: Partial<AdminTemplateEmail>): Promise<AdminTemplateEmail> {
    try {
      await this.repository.update(id, {
        ...data,
        updatedAt: Date.now(),
      });

      const updatedTemplate = await this.findById(id);
      if (!updatedTemplate) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy mẫu email với ID ${id}`,
        );
      }

      return updatedTemplate;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error updating email template: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể cập nhật mẫu email',
      );
    }
  }

  /**
   * Xóa template email
   * @param id ID của template cần xóa
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    try {
      const result = await this.repository.delete(id);
      return result.affected ? result.affected > 0 : false;
    } catch (error) {
      this.logger.error(`Error deleting email template: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể xóa mẫu email',
      );
    }
  }
}
