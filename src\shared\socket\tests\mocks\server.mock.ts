import { EventEmitter } from 'events';

/**
 * Mock cho Socket.IO server
 */
export class ServerMock extends EventEmitter {
  /**
   * Mock cho phương thức to
   * @param room Tên phòng
   * @returns this
   */
  to(room: string): any {
    return {
      emit: jest.fn(),
      except: (socketIds: string[]) => ({
        emit: jest.fn(),
      }),
    };
  }

  /**
   * Mock cho phương thức emit
   * @param event Tên sự kiện
   * @param data Dữ liệu
   * @returns boolean
   */
  emit(event: string, data: any): boolean {
    return true;
  }

  /**
   * Mock cho phương thức of
   * @param namespace Tên namespace
   * @returns this
   */
  of(namespace: string): any {
    return this;
  }
}
