import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsInt, IsNotEmpty, IsPositive } from 'class-validator';

/**
 * DTO để cập nhật quyền trực tiếp cho nhân viên
 */
export class UpdateEmployeePermissionDto {
  /**
   * ID của nhân viên cần cập nhật quyền
   */
  @ApiProperty({
    description: 'ID của nhân viên cần cập nhật quyền',
    example: 1,
  })
  @IsInt()
  @IsPositive()
  @IsNotEmpty()
  employeeId: number;

  /**
   * Danh sách ID của các quyền sẽ được gán trực tiếp cho nhân viên
   */
  @ApiProperty({
    description: 'Danh sách ID của các quyền sẽ được gán trực tiếp cho nhân viên',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray()
  @IsInt({ each: true })
  @IsPositive({ each: true })
  permissionIds: number[];
}

/**
 * DTO phản hồi sau khi cập nhật quyền cho nhân viên
 */
export class UpdateEmployeePermissionResponseDto {
  /**
   * ID của nhân viên đã được cập nhật quyền
   */
  @ApiProperty({
    description: 'ID của nhân viên đã được cập nhật quyền',
    example: 1,
  })
  employeeId: number;

  /**
   * ID của người dùng liên kết với nhân viên
   */
  @ApiProperty({
    description: 'ID của người dùng liên kết với nhân viên',
    example: 5,
  })
  userId: number;

  /**
   * Danh sách ID của các quyền đã được gán trực tiếp cho nhân viên
   */
  @ApiProperty({
    description: 'Danh sách ID của các quyền đã được gán trực tiếp cho nhân viên',
    example: [1, 2, 3],
    type: [Number],
  })
  permissionIds: number[];
}
