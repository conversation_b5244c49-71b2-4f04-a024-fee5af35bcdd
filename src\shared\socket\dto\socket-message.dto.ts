import { IsString, IsOptional, IsUUID, IsEnum, ValidateNested, IsObject } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Enum định nghĩa các loại tin nhắn
 */
export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  FILE = 'file',
  AUDIO = 'audio',
  VIDEO = 'video',
  LOCATION = 'location',
  SYSTEM = 'system',
}

/**
 * DTO cho metadata của tin nhắn
 */
export class MessageMetadataDto {
  @ApiProperty({ description: 'Kích thước file (nếu là file)', required: false })
  @IsOptional()
  @IsString()
  fileSize?: string;

  @ApiProperty({ description: 'Loại MIME (nếu là file)', required: false })
  @IsOptional()
  @IsString()
  mimeType?: string;

  @ApiProperty({ description: 'Chiều rộng (nếu là hình ảnh/video)', required: false })
  @IsOptional()
  @IsString()
  width?: string;

  @ApiProperty({ description: '<PERSON>ều cao (nếu là hình ảnh/video)', required: false })
  @IsOptional()
  @IsString()
  height?: string;

  @ApiProperty({ description: 'Thời lượng (nếu là audio/video)', required: false })
  @IsOptional()
  @IsString()
  duration?: string;

  @ApiProperty({ description: 'Vĩ độ (nếu là vị trí)', required: false })
  @IsOptional()
  @IsString()
  latitude?: string;

  @ApiProperty({ description: 'Kinh độ (nếu là vị trí)', required: false })
  @IsOptional()
  @IsString()
  longitude?: string;
}

/**
 * DTO cho tin nhắn Socket.IO
 */
export class SocketMessageDto {
  @ApiProperty({ description: 'Nội dung tin nhắn' })
  @IsString()
  content: string;

  @ApiProperty({ description: 'ID của phòng', required: false })
  @IsOptional()
  @IsString()
  roomId?: string;

  @ApiProperty({ description: 'ID của người nhận (nếu là tin nhắn 1-1)', required: false })
  @IsOptional()
  @IsString()
  recipientId?: string;

  @ApiProperty({ enum: MessageType, description: 'Loại tin nhắn', default: MessageType.TEXT })
  @IsEnum(MessageType)
  @IsOptional()
  type?: MessageType = MessageType.TEXT;

  @ApiProperty({ type: MessageMetadataDto, description: 'Metadata của tin nhắn', required: false })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => MessageMetadataDto)
  metadata?: MessageMetadataDto;
}
