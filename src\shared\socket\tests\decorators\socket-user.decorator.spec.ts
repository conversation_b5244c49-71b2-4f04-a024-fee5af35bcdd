import { ExecutionContext } from '@nestjs/common';
import { SocketUser } from '../../decorators/socket-user.decorator';
import { SocketMock, mockUser } from '../mocks';

describe('SocketUser', () => {
  it('should extract user from socket', () => {
    // Arrange
    const client = new SocketMock();
    client.setUser(mockUser);
    
    const context = {
      switchToWs: () => ({
        getClient: () => client,
      }),
    } as ExecutionContext;
    
    // Act
    const factory = SocketUser(null, context);
    
    // Assert
    expect(factory).toBe(mockUser);
  });

  it('should return undefined when user is not set', () => {
    // Arrange
    const client = new SocketMock();
    
    const context = {
      switchToWs: () => ({
        getClient: () => client,
      }),
    } as ExecutionContext;
    
    // Act
    const factory = SocketUser(null, context);
    
    // Assert
    expect(factory).toBeUndefined();
  });
});
