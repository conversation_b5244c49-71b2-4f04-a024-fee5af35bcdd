import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { KeyResult } from '../entities/key-result.entity';
import { KeyResultQueryDto } from '../dto/key-result/key-result-query.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';

/**
 * Repository for key results
 */
@Injectable()
export class KeyResultRepository {
  private readonly logger = new Logger(KeyResultRepository.name);

  constructor(
    @InjectRepository(KeyResult)
    private readonly repository: Repository<KeyResult>,
  ) {}

  /**
   * Find all key results with pagination and filtering
   * @param query Query parameters
   * @returns Paginated list of key results
   */
  async findAll(query: KeyResultQueryDto): Promise<PaginatedResult<KeyResult>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
      objectiveId,
      status
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('keyResult');

    // Apply filters if provided
    if (objectiveId) {
      queryBuilder.andWhere('keyResult.objectiveId = :objectiveId', { objectiveId });
    }

    if (status) {
      queryBuilder.andWhere('keyResult.status = :status', { status });
    }

    // Apply search filter if provided
    if (search) {
      queryBuilder.andWhere('keyResult.title ILIKE :search OR keyResult.description ILIKE :search',
        { search: `%${search}%` });
    }

    // Apply sorting
    queryBuilder.orderBy(`keyResult.${sortBy}`, sortDirection);

    // Apply pagination
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Find a key result by ID
   * @param id Key result ID
   * @returns Key result or null if not found
   */
  async findById(id: number): Promise<KeyResult | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Create a new key result
   * @param data Key result data
   * @returns Created key result
   */
  async create(data: Partial<KeyResult>): Promise<KeyResult> {
    const keyResult = this.repository.create(data);
    return this.repository.save(keyResult);
  }

  /**
   * Update a key result
   * @param id Key result ID
   * @param data Updated key result data
   * @returns Updated key result or null if not found
   */
  async update(id: number, data: Partial<KeyResult>): Promise<KeyResult | null> {
    await this.repository.update({ id }, data);
    return this.findById(id);
  }

  /**
   * Delete a key result
   * @param id Key result ID
   * @returns True if deleted, false if not found
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete({ id });
    return (result.affected ?? 0) > 0;
  }

  /**
   * Find key results by objective ID
   * @param objectiveId Objective ID
   * @returns List of key results
   */
  async findByObjectiveId(objectiveId: number): Promise<KeyResult[]> {
    return this.repository.find({
      where: { objectiveId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Update key result progress and current value
   * @param id Key result ID
   * @param currentValue Current value
   * @param progress Progress value (0-100)
   * @returns Updated key result or null if not found
   */
  async updateProgress(
    id: number,
    currentValue: number,
    progress: number
  ): Promise<KeyResult | null> {
    await this.repository.update(
      { id },
      { currentValue, progress, updatedAt: Date.now() }
    );
    return this.findById(id);
  }
}
