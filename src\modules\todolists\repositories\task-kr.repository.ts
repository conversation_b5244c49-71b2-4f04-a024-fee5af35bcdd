import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TaskKr } from '../entities/task-kr.entity';

/**
 * Repository cho entity TaskKr
 */
@Injectable()
export class TaskKrRepository {
  constructor(
    @InjectRepository(TaskKr)
    private readonly repository: Repository<TaskKr>,
  ) {}

  /**
   * Tạo liên kết giữa task và key result
   * @param data Dữ liệu liên kết
   * @returns Liên kết đã tạo
   */
  async create(data: Partial<TaskKr>): Promise<TaskKr> {
    const taskKr = this.repository.create(data);
    return this.repository.save(taskKr);
  }

  /**
   * Tạo nhiều liên kết giữa task và key result
   * @param dataArray Mảng dữ liệu liên kết
   * @returns Mảng liên kết đã tạo
   */
  async createMany(dataArray: Partial<TaskKr>[]): Promise<TaskKr[]> {
    const taskKrs = this.repository.create(dataArray);
    return this.repository.save(taskKrs);
  }

  /**
   * Tìm tất cả key result của một task
   * @param taskId ID của task
   * @returns Danh sách liên kết
   */
  async findByTaskId(taskId: number): Promise<TaskKr[]> {
    return this.repository.find({
      where: { taskId },
    });
  }

  /**
   * Tìm tất cả task của một key result
   * @param krId ID của key result
   * @returns Danh sách liên kết
   */
  async findByKrId(krId: number): Promise<TaskKr[]> {
    return this.repository.find({
      where: { krId },
    });
  }

  /**
   * Tìm liên kết giữa task và key result
   * @param taskId ID của task
   * @param krId ID của key result
   * @returns Liên kết hoặc null nếu không tìm thấy
   */
  async findByTaskIdAndKrId(taskId: number, krId: number): Promise<TaskKr | null> {
    return this.repository.findOne({
      where: {
        taskId,
        krId,
      },
    });
  }

  /**
   * Xóa liên kết giữa task và key result
   * @param taskId ID của task
   * @param krId ID của key result
   * @returns true nếu xóa thành công
   */
  async delete(taskId: number, krId: number): Promise<boolean> {
    const result = await this.repository.delete({
      taskId,
      krId,
    });
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Xóa tất cả liên kết của một task
   * @param taskId ID của task
   * @returns true nếu xóa thành công
   */
  async deleteByTaskId(taskId: number): Promise<boolean> {
    const result = await this.repository.delete({
      taskId,
    });
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }
}
