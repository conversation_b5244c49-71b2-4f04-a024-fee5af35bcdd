import { Global, Module, OnModuleInit } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { addTransactionalDataSource } from 'typeorm-transactional';

/**
 * Module cung cấp DataSource cho typeorm-transactional
 */
@Global()
@Module({})
export class TypeOrmTransactionalModule implements OnModuleInit {
  constructor(private readonly dataSource: DataSource) {}

  /**
   * Khi module khởi tạo, đăng ký DataSource với typeorm-transactional
   */
  onModuleInit() {
    // Đăng ký DataSource với typeorm-transactional
    addTransactionalDataSource(this.dataSource);
  }
}