import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TelegramBotService } from './telegram-bot.service';
import { TelegramMessage, TelegramUpdate } from './telegram.interface';
import { AppException, ErrorCode } from '@common/exceptions';

/**
 * Service kết nối bot Telegram với agent trong hệ thống
 */
@Injectable()
export class TelegramAgentService {
  private readonly logger = new Logger(TelegramAgentService.name);

  constructor(
    private readonly telegramBotService: TelegramBotService,
    private readonly configService: ConfigService
  ) {}

  /**
   * X<PERSON> lý tin nhắn từ Telegram và gửi đến agent
   * @param update Update từ Telegram
   * @param botId ID của bot trong hệ thống
   * @param agentId ID của agent trong hệ thống
   * @returns Kết quả xử lý
   */
  async processMessageWithAgent(
    update: TelegramUpdate,
    botId: number,
    agentId: string,
    botToken: string
  ): Promise<{ success: boolean; message?: string }> {
    try {
      // Kiểm tra xem update có chứa tin nhắn không
      if (!update.message) {
        return { success: true, message: 'Không có tin nhắn để xử lý' };
      }

      const message = update.message;
      const chatId = message.chat.id;

      // Lấy nội dung tin nhắn
      let content = '';
      if (message.text) {
        content = message.text;
      } else if (message.caption) {
        content = message.caption;
      } else if (message.photo) {
        content = '[Ảnh]';
        // Trong môi trường thực tế, bạn sẽ cần tải ảnh và xử lý
      } else if (message.document) {
        content = `[Tài liệu: ${message.document.file_name || 'Không có tên'}]`;
        // Trong môi trường thực tế, bạn sẽ cần tải document và xử lý
      } else {
        content = '[Nội dung không được hỗ trợ]';
      }

      // Trong môi trường thực tế, bạn sẽ cần gửi nội dung đến agent để xử lý
      // Đây là mô phỏng phản hồi từ agent
      const agentResponse = await this.simulateAgentResponse(content, agentId);

      // Gửi phản hồi từ agent về Telegram
      await this.telegramBotService.sendMessage(botToken, chatId, agentResponse);

      return { success: true, message: 'Đã xử lý tin nhắn với agent' };
    } catch (error) {
      this.logger.error(`Error processing message with agent: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        return { success: false, message: error.message };
      }
      return { success: false, message: 'Lỗi khi xử lý tin nhắn với agent' };
    }
  }

  /**
   * Mô phỏng phản hồi từ agent
   * @param content Nội dung tin nhắn
   * @param agentId ID của agent
   * @returns Phản hồi từ agent
   */
  private async simulateAgentResponse(content: string, agentId: string): Promise<string> {
    // Trong môi trường thực tế, bạn sẽ cần gọi API của agent để lấy phản hồi
    // Đây chỉ là mô phỏng
    return `Tôi đã nhận được tin nhắn của bạn: "${content}"\n\nĐây là phản hồi từ agent ${agentId}.`;
  }

  /**
   * Gửi tin nhắn từ agent đến Telegram
   * @param botToken Token của bot
   * @param chatId ID của cuộc trò chuyện
   * @param message Nội dung tin nhắn
   * @returns Thông tin về tin nhắn đã gửi
   */
  async sendAgentMessageToTelegram(
    botToken: string,
    chatId: number | string,
    message: string
  ): Promise<TelegramMessage> {
    try {
      return await this.telegramBotService.sendMessage(botToken, chatId, message, {
        parse_mode: 'HTML'
      });
    } catch (error) {
      this.logger.error(`Error sending agent message to Telegram: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn từ agent đến Telegram'
      );
    }
  }
}
