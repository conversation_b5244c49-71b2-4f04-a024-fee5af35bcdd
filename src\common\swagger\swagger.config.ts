import { DocumentBuilder, SwaggerCustomOptions } from '@nestjs/swagger';
import { SWAGGER_API_TAGS } from './swagger.tags';
import { ConfigService } from '@nestjs/config';

// Hàm tạo cấu hình Swagger với thông tin từ biến môi trường
export const createSwaggerConfig = (configService: ConfigService) => {
  // Lấy môi trường hiện tại (có thể sử dụng cho logic phức tạp hơn trong tương lai)
  // const nodeEnv = configService.get<string>('NODE_ENV', 'development');

  // Lấy URL server từ biến môi trường dựa trên môi trường hiện tại
  const localUrl = configService.get<string>('SWAGGER_LOCAL_URL', `http://localhost:${configService.get('PORT', 3000)}`);
  const devUrl = configService.get<string>('SWAGGER_DEV_URL', 'http://localhost:3000');
  const testUrl = configService.get<string>('SWAGGER_TEST_URL', 'http://*************:3000');
  const stagingUrl = configService.get<string>('SWAGGER_STAGING_URL', 'https://api-staging.redai.vn');
  const prodUrl = configService.get<string>('SWAGGER_PROD_URL', 'https://api.redai.vn');

  const builder = new DocumentBuilder()
    .setTitle('RedAI API')
    .setDescription(
      `
# RedAI API Documentation v1.0

Welcome to the RedAI API documentation. This API provides endpoints for managing tests, users, and blogs.

## API Version
Current version: v1

## Base URL
All API endpoints are prefixed with: \`/api/v1\`

## Authentication
Most endpoints require authentication. To authenticate, you need to:
1. Obtain a JWT token by logging in
2. Click the "Authorize" button at the top of this page
3. Enter your token in the format: \`Bearer your_token_here\`
4. Click "Authorize" and close the dialog

### Authentication for Protected Endpoints
Many endpoints require authentication. For these endpoints, you must provide a valid JWT token.

Public endpoints (like login, register, and those with 'public' in the path) do not require authentication.

## Rate Limiting
API calls are subject to rate limiting. Please refer to the response headers for rate limit information.

## Error Handling
The API uses standard HTTP status codes to indicate the success or failure of requests.
Common error codes:
- 400: Bad Request - The request was malformed or contains invalid parameters
- 401: Unauthorized - Authentication is required or has failed
- 403: Forbidden - The authenticated user does not have permission to access the requested resource
- 404: Not Found - The requested resource does not exist
- 429: Too Many Requests - Rate limit exceeded
- 500: Internal Server Error - An unexpected error occurred on the server
  `,
    )
    .setVersion('1.0')
    .setContact('RedAI Support', 'https://redai.com/support', '<EMAIL>')
    .setLicense('MIT', 'https://opensource.org/licenses/MIT')
    .setExternalDoc('Additional Documentation', 'https://redai.com/docs')
    .addServer(localUrl, 'Local Server')
    .addServer(devUrl, 'Development Server')
    .addServer(testUrl, 'Test Server')
    .addServer(stagingUrl, 'Staging Server')
    .addServer(prodUrl, 'Production Server')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth',
    );

  // Thêm các tag API
  Object.values(SWAGGER_API_TAGS).forEach(tag => {
    builder.addTag(tag.name, tag.description);
  });

  return builder.build();
};

export const swaggerCustomOptions: SwaggerCustomOptions = {
  swaggerOptions: {
    persistAuthorization: true,
    docExpansion: 'none',
    filter: true,
    tagsSorter: 'alpha',
    operationsSorter: 'alpha',
    defaultModelsExpandDepth: 1,
    defaultModelExpandDepth: 1,
    tryItOutEnabled: true,  // Enable Try it out by default
    displayRequestDuration: true,  // Show request duration
    showExtensions: true,
    showCommonExtensions: true,
  },
  customCss: `
  /* Thay nền topbar thành trắng, chữ và icon đỏ */
  .swagger-ui .topbar {
    background-color: #e53e3e;
    color: #ffffff;
  }
  .swagger-ui .topbar .download-url-wrapper .select-label select {
    border-color: #e53e3e;
    color: #e53e3e;
  }

  /* Tiêu đề API màu đỏ */
  .swagger-ui .info .title {
    color: #e53e3e;
  }

  /* Các opblock (GET, POST, PUT, DELETE, PATCH) chung nền trắng, viền đỏ nhạt */
  .swagger-ui .opblock {
    background: rgba(229, 62, 62, 0.1);
    border-color: #e53e3e;
  }
  .swagger-ui .opblock .opblock-summary-method {
    color: #ffffff;
  }

  /* Nút Execute màu đỏ tươi trên nền trắng */
  .swagger-ui .btn.execute {
    background-color: #e53e3e;
    color: #ffffff;
    border-color: #c53030;
  }
  .swagger-ui .btn.execute:hover {
    background-color: #c53030;
  }

  /* Các đường kẻ, nút mở rộng cũng về tông xám nhạt cho hài hòa */
  .swagger-ui .opblock .opblock-summary {
    border-bottom-color: #fde8e8;
  }
  .swagger-ui .parameter__name, .swagger-ui .response-col_status {
    color: #c53030;
  }
    `,
  customSiteTitle: 'RedAI API Documentation',
  customfavIcon: 'https://redai.com/favicon.ico',
};