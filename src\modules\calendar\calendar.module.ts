import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Calendar } from './entities/calendar.entity';
import { CalendarRepository } from './repositories/calendar.repository';
import { CalendarService } from './services/calendar.service';
import { CalendarIntegrationService } from './services/calendar-integration.service';
import { CalendarController } from './controllers/calendar.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([Calendar]),
  ],
  controllers: [CalendarController],
  providers: [
    CalendarService,
    CalendarRepository,
    CalendarIntegrationService,
  ],
  exports: [
    CalendarService,
    CalendarRepository,
    CalendarIntegrationService,
  ],
})
export class CalendarModule {}
