import { SystemEventType } from '../enum/system-event-type.enum';

export type SystemEventData =
  | {
      eventType: SystemEventType.STATUS_CHANGED;
      oldValue: string;
      newValue: string;
      actorId: number;
      changedAt: number;
    }
  | {
      eventType: SystemEventType.COLLABORATOR_CHANGED;
      action: 'added' | 'removed';
      userId: number;
      actorId: number;
      changedAt: number;
    }
  | {
      eventType: SystemEventType.ASSIGNEE_CHANGED;
      oldUserId: number | null;
      newUserId: number | null;
      actorId: number;
      changedAt: number;
    };
