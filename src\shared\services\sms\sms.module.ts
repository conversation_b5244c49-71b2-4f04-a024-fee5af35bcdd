import { Module, Global } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';

import { SmsService } from './sms.service';
import { SmsProviderFactory } from './sms-provider-factory.service';
import { SpeedSmsProvider } from './speed-sms-provider.service';
import { TwilioProvider } from './twilio-provider.service';
import { VonageProvider } from './vonage-provider.service';
import { FptSmsProvider } from './fpt-sms-provider.service';

@Global()
@Module({
  imports: [
    HttpModule,
    ConfigModule,
  ],
  providers: [
    SmsService,
    SmsProviderFactory,
    SpeedSmsProvider,
    TwilioProvider,
    VonageProvider,
    FptSmsProvider,
  ],
  exports: [
    SmsService,
    SmsProviderFactory,
    SpeedSmsProvider,
    TwilioProvider,
    VonageProvider,
    FptSmsProvider,
  ],
})
export class SmsModule {}
