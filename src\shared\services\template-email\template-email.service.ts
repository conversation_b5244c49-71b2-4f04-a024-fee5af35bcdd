import { Injectable, Logger } from '@nestjs/common';
import { EmailService } from '@shared/services/email';
import { AppException } from '@/common/exceptions/app.exception';
import { ErrorCode } from '@/common/exceptions/app.exception';

/**
 * DTO cho việc gửi email với template
 */
export interface SendTemplateEmailOptions {
  /**
   * Nội dung HTML của template
   */
  template: string;
  
  /**
   * Tiêu đề email
   */
  subject: string;
  
  /**
   * Email người nhận
   */
  to: string;
  
  /**
   * Dữ liệu để thay thế các placeholder trong template
   */
  data: Record<string, any>;
  
  /**
   * Email người gửi (tùy chọn)
   */
  from?: string;
  
  /**
   * Danh sách CC (tùy chọn)
   */
  cc?: string | string[];
  
  /**
   * Danh sách BCC (tùy chọn)
   */
  bcc?: string | string[];
  
  /**
   * Tệ<PERSON> đ<PERSON>h kè<PERSON> (tùy chọn)
   */
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
  }>;
}

/**
 * Service xử lý việc gửi email với template
 */
@Injectable()
export class TemplateEmailService {
  private readonly logger = new Logger(TemplateEmailService.name);

  constructor(private readonly emailService: EmailService) {}

  /**
   * Thay thế các placeholder trong nội dung template
   * @param content Nội dung template
   * @param data Dữ liệu thay thế
   * @returns Nội dung đã thay thế placeholder
   */
  private replacePlaceholders(content: string, data: Record<string, any>): string {
    let result = content;
    
    // Thay thế các placeholder dạng {{key}}
    Object.keys(data).forEach(key => {
      const placeholder = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      result = result.replace(placeholder, data[key]?.toString() || '');
    });
    
    return result;
  }

  /**
   * Gửi email sử dụng template
   * @param options Tùy chọn gửi email
   * @returns true nếu gửi thành công
   */
  async sendTemplateEmail(options: SendTemplateEmailOptions): Promise<boolean> {
    try {
      // Thay thế các placeholder trong tiêu đề và nội dung
      const subject = this.replacePlaceholders(options.subject, options.data);
      const html = this.replacePlaceholders(options.template, options.data);
      
      // Gửi email
      return await this.emailService.sendEmail({
        to: options.to,
        from: options.from,
        cc: options.cc,
        bcc: options.bcc,
        subject,
        html,
        attachments: options.attachments,
      });
    } catch (error) {
      this.logger.error(`Error sending template email: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.EMAIL_SENDING_ERROR,
        'Không thể gửi email',
      );
    }
  }
}
