import { Test, TestingModule } from '@nestjs/testing';
import { SocketService } from '../socket.service';
import { SocketMock, ServerMock, mockUser, mockAdminUser } from './mocks';
import { SocketEvents } from '../events/socket-events.enum';
import { Logger } from '@nestjs/common';

describe('SocketService', () => {
  let service: SocketService;
  let serverMock: ServerMock;

  beforeEach(async () => {
    // Tạo mock cho Logger
    jest.spyOn(Logger.prototype, 'log').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'debug').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'warn').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'error').mockImplementation(() => {});

    // Tạo module test
    const module: TestingModule = await Test.createTestingModule({
      providers: [SocketService],
    }).compile();

    // Lấy service từ module
    service = module.get<SocketService>(SocketService);
    
    // Tạo mock cho server
    serverMock = new ServerMock();
    service.setServer(serverMock as any);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('setServer', () => {
    it('should set server', () => {
      // Arrange
      const server = new ServerMock();
      
      // Act
      service.setServer(server as any);
      
      // Assert
      expect(service.getServer()).toBe(server);
    });
  });

  describe('addClient', () => {
    it('should add client without user', () => {
      // Arrange
      const client = new SocketMock('client-1');
      
      // Act
      service.addClient(client as any);
      
      // Assert
      expect(service['connectedClients'].get('client-1')).toBe(client);
    });

    it('should add client with user', () => {
      // Arrange
      const client = new SocketMock('client-1');
      
      // Act
      service.addClient(client as any, mockUser);
      
      // Assert
      expect(service['connectedClients'].get('client-1')).toBe(client);
      expect(service['userSocketMap'].get(mockUser.id)?.has('client-1')).toBeTruthy();
      expect(client.user).toBe(mockUser);
      expect(client.status).toBe('online');
    });

    it('should add multiple clients for same user', () => {
      // Arrange
      const client1 = new SocketMock('client-1');
      const client2 = new SocketMock('client-2');
      
      // Act
      service.addClient(client1 as any, mockUser);
      service.addClient(client2 as any, mockUser);
      
      // Assert
      expect(service['connectedClients'].get('client-1')).toBe(client1);
      expect(service['connectedClients'].get('client-2')).toBe(client2);
      expect(service['userSocketMap'].get(mockUser.id)?.has('client-1')).toBeTruthy();
      expect(service['userSocketMap'].get(mockUser.id)?.has('client-2')).toBeTruthy();
      expect(service['userSocketMap'].get(mockUser.id)?.size).toBe(2);
    });
  });

  describe('removeClient', () => {
    it('should remove client without user', () => {
      // Arrange
      const client = new SocketMock('client-1');
      service.addClient(client as any);
      
      // Act
      service.removeClient('client-1');
      
      // Assert
      expect(service['connectedClients'].has('client-1')).toBeFalsy();
    });

    it('should remove client with user', () => {
      // Arrange
      const client = new SocketMock('client-1');
      service.addClient(client as any, mockUser);
      
      // Act
      service.removeClient('client-1');
      
      // Assert
      expect(service['connectedClients'].has('client-1')).toBeFalsy();
      expect(service['userSocketMap'].has(mockUser.id)).toBeFalsy();
    });

    it('should remove one client but keep other clients for same user', () => {
      // Arrange
      const client1 = new SocketMock('client-1');
      const client2 = new SocketMock('client-2');
      service.addClient(client1 as any, mockUser);
      service.addClient(client2 as any, mockUser);
      
      // Act
      service.removeClient('client-1');
      
      // Assert
      expect(service['connectedClients'].has('client-1')).toBeFalsy();
      expect(service['connectedClients'].has('client-2')).toBeTruthy();
      expect(service['userSocketMap'].get(mockUser.id)?.has('client-1')).toBeFalsy();
      expect(service['userSocketMap'].get(mockUser.id)?.has('client-2')).toBeTruthy();
      expect(service['userSocketMap'].get(mockUser.id)?.size).toBe(1);
    });

    it('should do nothing when removing non-existent client', () => {
      // Act
      service.removeClient('non-existent');
      
      // Assert
      expect(service['connectedClients'].has('non-existent')).toBeFalsy();
    });
  });

  describe('addUserToRoom', () => {
    it('should add user to new room', () => {
      // Act
      service.addUserToRoom('room-1', mockUser.id);
      
      // Assert
      expect(service['roomUserMap'].get('room-1')?.has(mockUser.id)).toBeTruthy();
    });

    it('should add user to existing room', () => {
      // Arrange
      service.addUserToRoom('room-1', mockAdminUser.id);
      
      // Act
      service.addUserToRoom('room-1', mockUser.id);
      
      // Assert
      expect(service['roomUserMap'].get('room-1')?.has(mockAdminUser.id)).toBeTruthy();
      expect(service['roomUserMap'].get('room-1')?.has(mockUser.id)).toBeTruthy();
      expect(service['roomUserMap'].get('room-1')?.size).toBe(2);
    });
  });

  describe('removeUserFromRoom', () => {
    it('should remove user from room with multiple users', () => {
      // Arrange
      service.addUserToRoom('room-1', mockUser.id);
      service.addUserToRoom('room-1', mockAdminUser.id);
      
      // Act
      service.removeUserFromRoom('room-1', mockUser.id);
      
      // Assert
      expect(service['roomUserMap'].get('room-1')?.has(mockUser.id)).toBeFalsy();
      expect(service['roomUserMap'].get('room-1')?.has(mockAdminUser.id)).toBeTruthy();
      expect(service['roomUserMap'].get('room-1')?.size).toBe(1);
    });

    it('should remove room when removing last user', () => {
      // Arrange
      service.addUserToRoom('room-1', mockUser.id);
      
      // Act
      service.removeUserFromRoom('room-1', mockUser.id);
      
      // Assert
      expect(service['roomUserMap'].has('room-1')).toBeFalsy();
    });

    it('should do nothing when removing user from non-existent room', () => {
      // Act
      service.removeUserFromRoom('non-existent', mockUser.id);
      
      // Assert
      expect(service['roomUserMap'].has('non-existent')).toBeFalsy();
    });
  });

  describe('sendToUser', () => {
    it('should send message to connected user', () => {
      // Arrange
      const client = new SocketMock('client-1');
      service.addClient(client as any, mockUser);
      const emitSpy = jest.spyOn(serverMock, 'to').mockReturnValue({
        emit: jest.fn(),
      } as any);
      
      // Act
      service.sendToUser(mockUser.id, 'test-event', { message: 'test' });
      
      // Assert
      expect(emitSpy).toHaveBeenCalledWith('client-1');
    });

    it('should not send message to disconnected user', () => {
      // Arrange
      const warnSpy = jest.spyOn(Logger.prototype, 'warn');
      const emitSpy = jest.spyOn(serverMock, 'to');
      
      // Act
      service.sendToUser('non-existent', 'test-event', { message: 'test' });
      
      // Assert
      expect(warnSpy).toHaveBeenCalled();
      expect(emitSpy).not.toHaveBeenCalled();
    });
  });

  describe('sendToRoom', () => {
    it('should send message to all users in room', () => {
      // Arrange
      const emitSpy = jest.spyOn(serverMock, 'to').mockReturnValue({
        emit: jest.fn(),
      } as any);
      
      // Act
      service.sendToRoom('room-1', 'test-event', { message: 'test' });
      
      // Assert
      expect(emitSpy).toHaveBeenCalledWith('room-1');
    });

    it('should send message to all users in room except sender', () => {
      // Arrange
      const client = new SocketMock('client-1');
      service.addClient(client as any, mockUser);
      const exceptSpy = jest.fn().mockReturnValue({
        emit: jest.fn(),
      });
      const toSpy = jest.spyOn(serverMock, 'to').mockReturnValue({
        except: exceptSpy,
      } as any);
      
      // Act
      service.sendToRoom('room-1', 'test-event', { message: 'test' }, mockUser.id);
      
      // Assert
      expect(toSpy).toHaveBeenCalledWith('room-1');
      expect(exceptSpy).toHaveBeenCalledWith(['client-1']);
    });
  });

  describe('broadcastToAll', () => {
    it('should broadcast message to all users', () => {
      // Arrange
      const emitSpy = jest.spyOn(serverMock, 'emit');
      
      // Act
      service.broadcastToAll('test-event', { message: 'test' });
      
      // Assert
      expect(emitSpy).toHaveBeenCalled();
    });
  });

  describe('getUsersInRoom', () => {
    it('should return users in room', () => {
      // Arrange
      service.addUserToRoom('room-1', mockUser.id);
      service.addUserToRoom('room-1', mockAdminUser.id);
      
      // Act
      const users = service.getUsersInRoom('room-1');
      
      // Assert
      expect(users).toContain(mockUser.id);
      expect(users).toContain(mockAdminUser.id);
      expect(users.length).toBe(2);
    });

    it('should return empty array for non-existent room', () => {
      // Act
      const users = service.getUsersInRoom('non-existent');
      
      // Assert
      expect(users).toEqual([]);
    });
  });

  describe('getRoomsOfUser', () => {
    it('should return rooms of user', () => {
      // Arrange
      service.addUserToRoom('room-1', mockUser.id);
      service.addUserToRoom('room-2', mockUser.id);
      
      // Act
      const rooms = service.getRoomsOfUser(mockUser.id);
      
      // Assert
      expect(rooms).toContain('room-1');
      expect(rooms).toContain('room-2');
      expect(rooms.length).toBe(2);
    });

    it('should return empty array for user without rooms', () => {
      // Act
      const rooms = service.getRoomsOfUser('non-existent');
      
      // Assert
      expect(rooms).toEqual([]);
    });
  });

  describe('getSocketIdsOfUser', () => {
    it('should return socket IDs of connected user', () => {
      // Arrange
      const client1 = new SocketMock('client-1');
      const client2 = new SocketMock('client-2');
      service.addClient(client1 as any, mockUser);
      service.addClient(client2 as any, mockUser);
      
      // Act
      const socketIds = service.getSocketIdsOfUser(mockUser.id);
      
      // Assert
      expect(socketIds).toContain('client-1');
      expect(socketIds).toContain('client-2');
      expect(socketIds.length).toBe(2);
    });

    it('should return empty array for disconnected user', () => {
      // Act
      const socketIds = service.getSocketIdsOfUser('non-existent');
      
      // Assert
      expect(socketIds).toEqual([]);
    });
  });

  describe('getUserFromSocketId', () => {
    it('should return user from socket ID', () => {
      // Arrange
      const client = new SocketMock('client-1');
      service.addClient(client as any, mockUser);
      
      // Act
      const user = service.getUserFromSocketId('client-1');
      
      // Assert
      expect(user).toBe(mockUser);
    });

    it('should return undefined for non-existent socket ID', () => {
      // Act
      const user = service.getUserFromSocketId('non-existent');
      
      // Assert
      expect(user).toBeUndefined();
    });
  });

  describe('updateUserStatus', () => {
    it('should update status of connected user', () => {
      // Arrange
      const client = new SocketMock('client-1');
      service.addClient(client as any, mockUser);
      const broadcastSpy = jest.spyOn(service, 'broadcastToAll');
      
      // Act
      service.updateUserStatus(mockUser.id, 'away');
      
      // Assert
      expect(client.status).toBe('away');
      expect(broadcastSpy).toHaveBeenCalledWith(
        SocketEvents.USER_STATUS_CHANGED,
        expect.objectContaining({
          userId: mockUser.id,
          status: 'away',
        }),
      );
    });

    it('should do nothing for disconnected user', () => {
      // Arrange
      const broadcastSpy = jest.spyOn(service, 'broadcastToAll');
      
      // Act
      service.updateUserStatus('non-existent', 'away');
      
      // Assert
      expect(broadcastSpy).not.toHaveBeenCalled();
    });
  });
});
