import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { AxiosRequestConfig, AxiosResponse } from 'axios';
import { firstValueFrom } from 'rxjs';
import { PdfEditRequest, PdfPosition } from '@shared/interface/pdf.interface';
import { AppException, ErrorCode } from '@/common';

@Injectable()
export class PdfService {
    private readonly logger = new Logger(PdfService.name);
    private readonly pdfApiBaseUrl: string;

    constructor(
        private readonly httpService: HttpService,
        private readonly configService: ConfigService,
    ) {
        let apiUrl = this.configService.get<string>('PDF_EDIT_API_URL');
        if (!apiUrl) {
            throw new AppException(ErrorCode.PDF_PROCESSING_ERROR, 'PDF_EDIT_API_URL environment variable is not defined.');
        }
        // Đảm bảo URL không có dấu / ở cuối
        if (apiUrl.endsWith('/')) {
            apiUrl = apiUrl.slice(0, -1);
        }
        this.pdfApiBaseUrl = apiUrl; // Gán sau khi kiểm tra và xử lý
    }

    /**
     * Gọi API bên thứ 3 để chỉnh sửa file PDF.
     * @param pdfBytes Nội dung PDF gốc (Buffer hoặc Base64 string).
     * @param positions Danh sách các vị trí cần chỉnh sửa.
     * @returns Promise<Buffer> Nội dung file PDF đã chỉnh sửa dưới dạng Buffer.
     */
    async editPdf(pdfBytes: Buffer | string, positions: PdfPosition[]): Promise<Buffer> {
        const apiUrl = `${this.pdfApiBaseUrl}/api/v1/pdf/edit`;
        this.logger.log(`Calling PDF edit API at: ${apiUrl}`);

        let pdfBase64: string;
        if (Buffer.isBuffer(pdfBytes)) {
            pdfBase64 = pdfBytes.toString('base64');
        } else {
            pdfBase64 = pdfBytes; // Giả sử đã là Base64 nếu không phải Buffer
        }

        const requestData: PdfEditRequest = {
            pdfBytes: pdfBase64,
            positions: positions,
        };

        const config: AxiosRequestConfig = {
            responseType: 'arraybuffer', // Yêu cầu Axios trả về ArrayBuffer cho dữ liệu nhị phân
        };

        try {
            const response: AxiosResponse<ArrayBuffer> = await firstValueFrom(
                this.httpService.post<ArrayBuffer>(apiUrl, requestData, config),
            );

            // Chuyển đổi ArrayBuffer nhận được thành Buffer của Node.js
            const editedPdfBuffer = Buffer.from(response.data);
            this.logger.log('Successfully received edited PDF from API.');
            return editedPdfBuffer;
        } catch (error) {
            this.logger.error(
                `Failed to edit PDF via API. Status: ${error.response?.status}, Data: ${error.response?.data?.toString()}`,
                error.stack,
            );
            // Ném lỗi với AppException
            throw new AppException(ErrorCode.PDF_PROCESSING_ERROR, `PDF editing failed: ${error.message}`, error);
        }
    }
}