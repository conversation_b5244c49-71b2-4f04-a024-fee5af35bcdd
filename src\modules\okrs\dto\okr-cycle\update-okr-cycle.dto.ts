import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsEnum, IsOptional, IsString } from 'class-validator';
import { OkrCycleStatus } from '../../enum/okr-cycle-status.enum';

/**
 * DTO for updating an OKR cycle
 */
export class UpdateOkrCycleDto {
  /**
   * Name of the OKR cycle
   * @example "Q1-2025"
   */
  @ApiProperty({
    description: 'Tên chu kỳ OKR',
    example: 'Q1-2025',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  /**
   * Start date of the OKR cycle
   * @example "2025-01-01"
   */
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> bắt đầu chu kỳ',
    example: '2025-01-01',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  /**
   * End date of the OKR cycle
   * @example "2025-03-31"
   */
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> kết thúc chu kỳ',
    example: '2025-03-31',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  endDate?: string;

  /**
   * Status of the OKR cycle
   * @example "ACTIVE"
   */
  @ApiProperty({
    description: 'Trạng thái chu kỳ',
    enum: OkrCycleStatus,
    example: OkrCycleStatus.ACTIVE,
    required: false,
  })
  @IsEnum(OkrCycleStatus)
  @IsOptional()
  status?: OkrCycleStatus;

  /**
   * Description of the OKR cycle
   * @example "Chu kỳ Q1 năm 2025 đã cập nhật"
   */
  @ApiProperty({
    description: 'Mô tả chu kỳ',
    example: 'Chu kỳ Q1 năm 2025 đã cập nhật',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;
}
