import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiExtraModels,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { RequirePermissionEnum } from '@/modules/auth/decorators/require-module-action.decorator';
import { Permission } from '@/modules/auth/enum/permission.enum';
import { RoleService } from '../services/role.service';
import { RoleQueryDto, RoleListItemDto, RoleDetailDto } from '../dto/role/role-list.dto';
import { UserRolesResponseDto } from '../dto/role/user-roles.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';

/**
 * Controller quản lý vai trò
 */
@ApiTags(SWAGGER_API_TAG.HRM)
@ApiExtraModels(
  ApiResponseDto,
  RoleListItemDto,
  RoleDetailDto,
  UserRolesResponseDto,
  PaginatedResult
)
@Controller('api/hrm/roles')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class RoleController {
  constructor(private readonly roleService: RoleService) {}

  /**
   * Lấy danh sách vai trò với phân trang và tìm kiếm
   */
  @Get()
  @RequirePermissionEnum(Permission.ROLE_VIEW_LIST)
  @ApiOperation({ summary: 'Lấy danh sách vai trò' })
  @ApiQuery({ name: 'page', required: false, description: 'Số trang', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Số lượng mỗi trang', type: Number })
  @ApiQuery({ name: 'search', required: false, description: 'Từ khóa tìm kiếm', type: String })
  @ApiQuery({ name: 'name', required: false, description: 'Tên vai trò', type: String })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Sắp xếp theo trường', type: String })
  @ApiQuery({ name: 'sortDirection', required: false, description: 'Hướng sắp xếp (ASC, DESC)', type: String })
  @ApiResponse({
    status: 200,
    description: 'Danh sách vai trò',
    schema: ApiResponseDto.getPaginatedSchema(RoleListItemDto),
  })
  async findAll(
    @Query() query: RoleQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<RoleListItemDto>>> {
    const result = await this.roleService.findAll(query);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Lấy thông tin chi tiết của vai trò bao gồm danh sách quyền
   */
  @Get(':id')
  @RequirePermissionEnum(Permission.ROLE_VIEW_DETAIL)
  @ApiOperation({ summary: 'Lấy thông tin chi tiết của vai trò bao gồm danh sách quyền' })
  @ApiParam({ name: 'id', description: 'ID của vai trò', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết của vai trò',
    schema: ApiResponseDto.getSchema(RoleDetailDto),
  })
  async findById(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<RoleDetailDto>> {
    const result = await this.roleService.findById(id);
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy danh sách vai trò của người dùng hiện tại
   */
  @Get('user/me')
  @ApiOperation({ summary: 'Lấy danh sách vai trò của người dùng hiện tại' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách vai trò của người dùng hiện tại',
    schema: ApiResponseDto.getSchema(UserRolesResponseDto),
  })
  async getCurrentUserRoles(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UserRolesResponseDto>> {
    const result = await this.roleService.getUserRoles(user.id);
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy danh sách vai trò của người dùng theo ID
   */
  @Get('user/:userId')
  @RequirePermissionEnum(Permission.USER_VIEW_DETAIL)
  @ApiOperation({ summary: 'Lấy danh sách vai trò của người dùng theo ID' })
  @ApiParam({ name: 'userId', description: 'ID của người dùng', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách vai trò của người dùng',
    schema: ApiResponseDto.getSchema(UserRolesResponseDto),
  })
  async getUserRoles(
    @Param('userId', ParseIntPipe) userId: number,
  ): Promise<ApiResponseDto<UserRolesResponseDto>> {
    const result = await this.roleService.getUserRoles(userId);
    return ApiResponseDto.success(result);
  }
}
