import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsDate,
  MaxLength,
  IsNumber,
  Min,
  IsPositive,
  IsInt,
  IsDecimal
} from 'class-validator';
import { Type } from 'class-transformer';
import { ContractStatus } from '../enum/contract-status.enum';
import { ContractType } from '../enum/contract-type.enum';

/**
 * DTO for updating a contract
 */
export class UpdateContractDto {
  /**
   * Contract type
   * @example "definite"
   */
  @ApiProperty({
    required: false,
    description: 'Contract type',
    enum: ContractType,
    example: ContractType.DEFINITE
  })
  @IsOptional()
  @IsEnum(ContractType)
  contractType?: ContractType;

  /**
   * Contract title
   * @example "Updated Employment Contract"
   */
  @ApiProperty({ required: false, description: 'Contract title', example: 'Updated Employment Contract' })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  title?: string;

  /**
   * Contract description
   * @example "Updated standard employment contract for software engineers"
   */
  @ApiProperty({
    required: false,
    description: 'Contract description',
    example: 'Updated standard employment contract for software engineers'
  })
  @IsOptional()
  @IsString()
  description?: string;

  /**
   * Contract start date
   * @example "2023-02-01"
   */
  @ApiProperty({ required: false, description: 'Contract start date', example: '2023-02-01' })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  startDate?: Date;

  /**
   * Contract end date (null for indefinite contracts)
   * @example "2024-02-01"
   */
  @ApiProperty({
    required: false,
    description: 'Contract end date (null for indefinite contracts)',
    example: '2024-02-01'
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  endDate?: Date;

  /**
   * Contract signing date
   * @example "2023-01-25"
   */
  @ApiProperty({ required: false, description: 'Contract signing date', example: '2023-01-25' })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  signingDate?: Date;

  /**
   * Contract status
   * @example "active"
   */
  @ApiProperty({
    required: false,
    description: 'Contract status',
    enum: ContractStatus,
    example: ContractStatus.ACTIVE
  })
  @IsOptional()
  @IsEnum(ContractStatus)
  status?: ContractStatus;

  /**
   * Base salary amount
   * @example 12000000
   */
  @ApiProperty({ required: false, description: 'Base salary amount', example: 12000000 })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  @Type(() => Number)
  baseSalary?: number;

  /**
   * Salary currency
   * @example "VND"
   */
  @ApiProperty({ required: false, description: 'Salary currency', example: 'VND' })
  @IsOptional()
  @IsString()
  @MaxLength(10)
  currency?: string;

  /**
   * Working hours per week
   * @example 35
   */
  @ApiProperty({ required: false, description: 'Working hours per week', example: 35 })
  @IsOptional()
  @IsInt()
  @IsPositive()
  @Type(() => Number)
  workingHoursPerWeek?: number;

  /**
   * Probation period in days (if applicable)
   * @example 30
   */
  @ApiProperty({ required: false, description: 'Probation period in days (if applicable)', example: 30 })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  probationPeriodDays?: number;

  /**
   * Notice period in days
   * @example 45
   */
  @ApiProperty({ required: false, description: 'Notice period in days', example: 45 })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  noticePeriodDays?: number;

  /**
   * Path to contract document file
   * @example "contracts/2023/CT-2023-001-updated.pdf"
   */
  @ApiProperty({
    required: false,
    description: 'Path to contract document file',
    example: 'contracts/2023/CT-2023-001-updated.pdf'
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  documentPath?: string;

  /**
   * Notes about the contract
   * @example "Contract updated with new terms for hybrid work"
   */
  @ApiProperty({
    required: false,
    description: 'Notes about the contract',
    example: 'Contract updated with new terms for hybrid work'
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
