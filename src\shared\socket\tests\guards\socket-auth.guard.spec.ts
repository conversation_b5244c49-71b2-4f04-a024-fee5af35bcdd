import { Test, TestingModule } from '@nestjs/testing';
import { SocketAuthGuard } from '../../guards/socket-auth.guard';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { WsException } from '@nestjs/websockets';
import { SocketMock, JwtServiceMock, ConfigServiceMock, mockUser } from '../mocks';
import { ExecutionContext, Logger } from '@nestjs/common';

describe('SocketAuthGuard', () => {
  let guard: SocketAuthGuard;
  let jwtService: JwtService;

  beforeEach(async () => {
    // Tạo mock cho Logger
    jest.spyOn(Logger.prototype, 'log').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'warn').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'error').mockImplementation(() => {});

    // Tạo module test
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SocketAuthGuard,
        {
          provide: JwtService,
          useClass: JwtServiceMock,
        },
        {
          provide: ConfigService,
          useClass: ConfigServiceMock,
        },
      ],
    }).compile();

    // Lấy guard và service từ module
    guard = module.get<SocketAuthGuard>(SocketAuthGuard);
    jwtService = module.get<JwtService>(JwtService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  describe('canActivate', () => {
    it('should return true when client already has user', () => {
      // Arrange
      const client = new SocketMock();
      client.setUser(mockUser);
      
      const context = {
        switchToWs: () => ({
          getClient: () => client,
        }),
      } as ExecutionContext;
      
      // Act
      const result = guard.canActivate(context);
      
      // Assert
      expect(result).toBe(true);
    });

    it('should return true when token is valid in auth', () => {
      // Arrange
      const client = new SocketMock();
      client.setAuthToken('valid-token');
      
      const context = {
        switchToWs: () => ({
          getClient: () => client,
        }),
      } as ExecutionContext;
      
      const verifySpy = jest.spyOn(jwtService, 'verify');
      
      // Act
      const result = guard.canActivate(context);
      
      // Assert
      expect(result).toBe(true);
      expect(verifySpy).toHaveBeenCalledWith('valid-token', expect.any(Object));
      expect(client.user).toEqual(mockUser);
    });

    it('should return true when token is valid in headers', () => {
      // Arrange
      const client = new SocketMock();
      client.setAuthHeader('valid-token');
      
      const context = {
        switchToWs: () => ({
          getClient: () => client,
        }),
      } as ExecutionContext;
      
      const verifySpy = jest.spyOn(jwtService, 'verify');
      
      // Act
      const result = guard.canActivate(context);
      
      // Assert
      expect(result).toBe(true);
      expect(verifySpy).toHaveBeenCalledWith('valid-token', expect.any(Object));
      expect(client.user).toEqual(mockUser);
    });

    it('should return true when token is valid in query', () => {
      // Arrange
      const client = new SocketMock();
      client.setAuthQuery('valid-token');
      
      const context = {
        switchToWs: () => ({
          getClient: () => client,
        }),
      } as ExecutionContext;
      
      const verifySpy = jest.spyOn(jwtService, 'verify');
      
      // Act
      const result = guard.canActivate(context);
      
      // Assert
      expect(result).toBe(true);
      expect(verifySpy).toHaveBeenCalledWith('valid-token', expect.any(Object));
      expect(client.user).toEqual(mockUser);
    });

    it('should throw WsException when token is missing', () => {
      // Arrange
      const client = new SocketMock();
      
      const context = {
        switchToWs: () => ({
          getClient: () => client,
        }),
      } as ExecutionContext;
      
      // Act & Assert
      expect(() => guard.canActivate(context)).toThrow(WsException);
    });

    it('should throw WsException when token is invalid', () => {
      // Arrange
      const client = new SocketMock();
      client.setAuthToken('invalid-token');
      
      const context = {
        switchToWs: () => ({
          getClient: () => client,
        }),
      } as ExecutionContext;
      
      jest.spyOn(jwtService, 'verify').mockImplementation(() => {
        throw new Error('Invalid token');
      });
      
      // Act & Assert
      expect(() => guard.canActivate(context)).toThrow(WsException);
    });
  });

  describe('extractToken', () => {
    it('should extract token from auth', () => {
      // Arrange
      const client = new SocketMock();
      client.setAuthToken('token-from-auth');
      
      // Act
      const token = (guard as any).extractToken(client);
      
      // Assert
      expect(token).toBe('token-from-auth');
    });

    it('should extract token from headers', () => {
      // Arrange
      const client = new SocketMock();
      client.setAuthHeader('token-from-headers');
      
      // Act
      const token = (guard as any).extractToken(client);
      
      // Assert
      expect(token).toBe('token-from-headers');
    });

    it('should extract token from query', () => {
      // Arrange
      const client = new SocketMock();
      client.setAuthQuery('token-from-query');
      
      // Act
      const token = (guard as any).extractToken(client);
      
      // Assert
      expect(token).toBe('token-from-query');
    });

    it('should return null when token is missing', () => {
      // Arrange
      const client = new SocketMock();
      
      // Act
      const token = (guard as any).extractToken(client);
      
      // Assert
      expect(token).toBeNull();
    });
  });
});
