import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString, IsDateString } from 'class-validator';
import { ObjectiveType } from '../../enum/objective-type.enum';
import { QueryDto } from '@common/dto/query.dto';

/**
 * DTO for querying objectives
 */
export class ObjectiveQueryDto extends QueryDto {
  /**
   * Filter by objective type
   * @example "COMPANY"
   */
  @ApiProperty({
    description: 'Lọc theo loại mục tiêu',
    enum: ObjectiveType,
    required: false,
  })
  @IsOptional()
  @IsEnum(ObjectiveType)
  type?: ObjectiveType;

  /**
   * Filter by OKR cycle ID
   * @example 1
   */
  @ApiProperty({
    description: 'Lọc theo ID chu kỳ OKR',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  cycleId?: number;

  /**
   * Filter by owner ID
   * @example 1
   */
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> theo ID người chịu trách nhiệm',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  ownerId?: number;

  /**
   * Filter by department ID
   * @example 2
   */
  @ApiProperty({
    description: 'Lọc theo ID phòng ban',
    example: 2,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  departmentId?: number;

  /**
   * Filter by status
   * @example "active"
   */
  @ApiProperty({
    description: 'Lọc theo trạng thái',
    example: 'active',
    required: false,
  })
  @IsOptional()
  @IsString()
  status?: string;

  /**
   * Filter by parent ID
   * @example 5
   */
  @ApiProperty({
    description: 'Lọc theo ID mục tiêu cha',
    example: 5,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  parentId?: number;

  /**
   * Filter by start date (format: YYYY-MM-DD)
   * @example "2025-01-01"
   */
  @ApiProperty({
    description: 'Lọc theo ngày bắt đầu (định dạng: YYYY-MM-DD)',
    example: '2025-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  /**
   * Filter by end date (format: YYYY-MM-DD)
   * @example "2025-12-31"
   */
  @ApiProperty({
    description: 'Lọc theo ngày kết thúc (định dạng: YYYY-MM-DD)',
    example: '2025-12-31',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;
}
