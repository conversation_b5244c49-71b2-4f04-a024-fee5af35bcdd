import { JwtPayload, TokenType } from '../../interfaces/jwt-payload.interface';
import { mockUser } from './user.mock';

/**
 * Mock cho JwtService
 */
export class JwtServiceMock {
  /**
   * Mock cho phương thức sign
   * @param payload Dữ liệu cần ký
   * @param options Tùy chọn
   * @returns Token JWT
   */
  sign(payload: any, options?: any): string {
    return 'mock-jwt-token';
  }

  /**
   * Mock cho phương thức verify
   * @param token Token JWT
   * @param options Tùy chọn
   * @returns Dữ liệu đã giải mã
   */
  verify(token: string, options?: any): JwtPayload {
    if (token === 'invalid-token') {
      throw new Error('Invalid token');
    }
    return mockUser;
  }

  /**
   * Mock cho phương thức decode
   * @param token Token JWT
   * @param options Tùy chọn
   * @returns Dữ liệu đã giải mã
   */
  decode(token: string, options?: any): JwtPayload | null {
    if (token === 'invalid-token') {
      return null;
    }
    return mockUser;
  }
}

/**
 * Mock cho ConfigService
 */
export class ConfigServiceMock {
  /**
   * Mock cho phương thức get
   * @param key Khóa cấu hình
   * @param defaultValue Giá trị mặc định
   * @returns Giá trị cấu hình
   */
  get<T>(key: string, defaultValue?: T): T {
    const config: Record<string, any> = {
      JWT_SECRET: 'mock-jwt-secret',
      JWT_EXPIRES_IN: '1d',
    };
    return (config[key] || defaultValue) as T;
  }
}
