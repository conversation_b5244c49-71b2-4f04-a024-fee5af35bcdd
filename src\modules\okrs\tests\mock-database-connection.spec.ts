import { Test, TestingModule } from '@nestjs/testing';
import { DataSource } from 'typeorm';
import { Logger } from '@nestjs/common';

/**
 * Mock DataSource class for testing
 */
class MockDataSource {
  options = {
    type: 'postgres',
    host: 'localhost',
    port: 5432,
    username: 'test_user',
    password: 'test_password',
    database: 'test_db',
    autoLoadEntities: true,
    synchronize: false,
    ssl: {
      rejectUnauthorized: false,
    },
  };
  isInitialized = true;
  
  async destroy() {
    return Promise.resolve();
  }
  
  async query(sql: string) {
    if (sql === 'SELECT 1 as value') {
      return [{ value: 1 }];
    }
    return [];
  }
}

/**
 * Test kết nối database với mock DataSource
 */
describe('Mock Database Connection Test', () => {
  let module: TestingModule;
  let dataSource: MockDataSource;
  const logger = new Logger('MockDatabaseConnectionTest');

  beforeAll(async () => {
    // Tạo testing module với mock DataSource
    module = await Test.createTestingModule({
      providers: [
        {
          provide: DataSource,
          useClass: MockDataSource,
        },
      ],
    }).compile();

    // Lấy mock DataSource
    dataSource = module.get<MockDataSource>(DataSource);
  });

  afterAll(async () => {
    // Đóng kết nối sau khi test xong
    if (dataSource && dataSource.isInitialized) {
      await dataSource.destroy();
    }
    await module.close();
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
    expect(dataSource).toBeDefined();
  });

  it('should have the correct database configuration', () => {
    // Kiểm tra cấu hình database
    expect(dataSource.options).toMatchObject({
      type: 'postgres',
      host: 'localhost',
      port: 5432,
      username: 'test_user',
      password: 'test_password',
      database: 'test_db',
      ssl: {
        rejectUnauthorized: false,
      },
    });
  });

  it('should have autoLoadEntities set to true', () => {
    expect(dataSource.options.autoLoadEntities).toBe(true);
  });

  it('should have synchronize set to false', () => {
    expect(dataSource.options.synchronize).toBe(false);
  });

  it('should be able to execute a simple query', async () => {
    // Thử thực hiện một truy vấn đơn giản
    const result = await dataSource.query('SELECT 1 as value');
    expect(result).toBeDefined();
    expect(result[0].value).toBe(1);
  });
});
