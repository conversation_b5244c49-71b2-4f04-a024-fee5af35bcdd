import { Controller, Get, Post, Body, Query, Logger, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { RedisService } from '@shared/services/redis.service';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';

/**
 * Controller để kiểm tra kết nối và thao tác với Redis
 */
@Controller('system/redis-test')
@ApiTags(SWAGGER_API_TAG.SYSTEM)
export class RedisTestController {
  private readonly logger = new Logger(RedisTestController.name);

  constructor(private readonly redisService: RedisService) {}

  /**
   * Kiểm tra kết nối tới Redis
   */
  @Get('ping')
  @ApiOperation({ summary: 'Kiểm tra kết nối tới Redis' })
  @ApiResponse({
    status: 200,
    description: 'Kết nối thành công',
    schema: ApiResponseDto.getSchema({ connected: true, message: 'Redis connection successful' }),
  })
  async ping(): Promise<ApiResponseDto<{ connected: boolean; message: string }>> {
    try {
      // Thử thực hiện một thao tác đơn giản với Redis để kiểm tra kết nối
      const testKey = 'redis_test_ping_' + Date.now();
      const testValue = 'ping_test';

      // Lưu giá trị vào Redis
      await this.redisService.setWithExpiry(testKey, testValue, 10); // Hết hạn sau 10 giây

      // Lấy giá trị từ Redis
      const retrievedValue = await this.redisService.get(testKey);

      // Xóa key test
      await this.redisService.del(testKey);

      return ApiResponseDto.success({
        connected: retrievedValue === testValue,
        message: `Redis connection successful. Test value: ${retrievedValue}`,
      });
    } catch (error) {
      this.logger.error('Redis connection test failed:', error);

      return ApiResponseDto.success({
        connected: false,
        message: `Redis connection failed: ${error.message}`,
      });
    }
  }

  /**
   * Kiểm tra lưu trữ và lấy dữ liệu từ Redis
   */
  @Post('set-get')
  @ApiOperation({ summary: 'Kiểm tra lưu trữ và lấy dữ liệu từ Redis' })
  @ApiResponse({
    status: 200,
    description: 'Kết quả kiểm tra',
    schema: ApiResponseDto.getSchema({
      setResult: 'OK',
      getValue: 'test-value',
      success: true
    }),
  })
  async setAndGet(
    @Body() body: { key: string; value: string; expiryInSeconds?: number },
  ): Promise<ApiResponseDto<{ setResult: string; getValue: string | null; success: boolean }>> {
    try {
      let setResult: string;

      // Lưu giá trị vào Redis
      if (body.expiryInSeconds) {
        setResult = await this.redisService.setWithExpiry(
          body.key,
          body.value,
          body.expiryInSeconds,
        );
      } else {
        setResult = await this.redisService.set(body.key, body.value);
      }

      // Lấy giá trị từ Redis
      const getValue = await this.redisService.get(body.key);

      return ApiResponseDto.success({
        setResult,
        getValue,
        success: getValue === body.value,
      });
    } catch (error) {
      this.logger.error('Redis set-get test failed:', error);

      return ApiResponseDto.success({
        setResult: `Error: ${error.message}`,
        getValue: null,
        success: false,
      });
    }
  }

  /**
   * Kiểm tra lấy dữ liệu từ Redis
   */
  @Get('get')
  @ApiOperation({ summary: 'Kiểm tra lấy dữ liệu từ Redis' })
  @ApiResponse({
    status: 200,
    description: 'Kết quả kiểm tra',
    schema: ApiResponseDto.getSchema({ value: 'test-value', exists: true }),
  })
  async get(
    @Query('key') key: string,
  ): Promise<ApiResponseDto<{ value: string | null; exists: boolean }>> {
    try {
      // Kiểm tra xem key có tồn tại không
      const exists = await this.redisService.exists(key);

      // Lấy giá trị từ Redis
      const value = await this.redisService.get(key);

      return ApiResponseDto.success({
        value,
        exists,
      });
    } catch (error) {
      this.logger.error('Redis get test failed:', error);

      return ApiResponseDto.success({
        value: `Error: ${error.message}`,
        exists: false,
      });
    }
  }

  /**
   * Kiểm tra xóa dữ liệu từ Redis
   */
  @Post('delete')
  @ApiOperation({ summary: 'Kiểm tra xóa dữ liệu từ Redis' })
  @ApiResponse({
    status: 200,
    description: 'Kết quả kiểm tra',
    schema: ApiResponseDto.getSchema({ deleted: 1, success: true }),
  })
  async delete(
    @Body() body: { key: string },
  ): Promise<ApiResponseDto<{ deleted: number; success: boolean }>> {
    try {
      // Xóa key từ Redis
      const deleted = await this.redisService.del(body.key);

      return ApiResponseDto.success({
        deleted,
        success: deleted > 0,
      });
    } catch (error) {
      this.logger.error('Redis delete test failed:', error);

      return ApiResponseDto.success({
        deleted: 0,
        success: false,
      });
    }
  }

  /**
   * Kiểm tra key OTP cụ thể
   */
  @Get('check-otp-key')
  @ApiOperation({ summary: 'Kiểm tra key OTP cụ thể' })
  @ApiResponse({
    status: 200,
    description: 'Kết quả kiểm tra',
    schema: ApiResponseDto.getSchema({
      exists: true,
      value: '{"companyId":123,"email":"<EMAIL>","otp":"123456"}',
      decodedValue: { companyId: 123, email: '<EMAIL>', otp: '123456' }
    }),
  })
  async checkOtpKey(
    @Query('token') token: string,
    @Query('prefix') prefix: string = 'company_registration:',
  ): Promise<ApiResponseDto<{ exists: boolean; value: string | null; decodedValue: any }>> {
    try {
      const key = `${prefix}${token}`;

      // Kiểm tra xem key có tồn tại không
      const exists = await this.redisService.exists(key);

      // Lấy giá trị từ Redis
      const value = await this.redisService.get(key);

      // Thử parse JSON nếu có giá trị
      let decodedValue = null;
      if (value) {
        try {
          decodedValue = JSON.parse(value);
        } catch (e) {
          this.logger.warn(`Could not parse JSON value for key ${key}: ${e.message}`);
        }
      }

      return ApiResponseDto.success({
        exists,
        value,
        decodedValue,
      });
    } catch (error) {
      this.logger.error('Redis check OTP key test failed:', error);

      return ApiResponseDto.success({
        exists: false,
        value: `Error: ${error.message}`,
        decodedValue: null,
      });
    }
  }
}
