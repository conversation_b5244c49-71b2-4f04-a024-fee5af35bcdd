import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi thông tin liên kết giữa todo và label
 */
export class TodoTagResponseDto {
  /**
   * ID của liên kết
   * @example 1
   */
  @ApiProperty({
    description: 'ID của liên kết',
    example: 1,
  })
  id: number;

  /**
   * ID của todo
   * @example 1
   */
  @ApiProperty({
    description: 'ID của todo',
    example: 1,
    nullable: true,
  })
  todoId: number | null;

  /**
   * ID của label
   * @example 1
   */
  @ApiProperty({
    description: 'ID của label',
    example: 1,
    nullable: true,
  })
  labelsId: number | null;

  /**
   * Thời gian tạo liên kết (timestamp)
   * @example 1625097600000
   */
  @ApiProperty({
    description: 'Thời gian tạo liên kết (timestamp)',
    example: 1625097600000,
    nullable: true,
  })
  createdAt: number | null;
}
