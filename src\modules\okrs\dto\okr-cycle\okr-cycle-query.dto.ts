import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsDateString } from 'class-validator';
import { OkrCycleStatus } from '../../enum/okr-cycle-status.enum';
import { QueryDto } from '@/common/dto/query.dto';

/**
 * DTO for querying OKR cycles
 */
export class OkrCycleQueryDto extends QueryDto {
  /**
   * Filter by OKR cycle status
   * @example "ACTIVE"
   */
  @ApiProperty({
    description: 'Lọc theo trạng thái chu kỳ',
    enum: OkrCycleStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(OkrCycleStatus)
  status?: OkrCycleStatus;

  /**
   * Filter by start date (format: YYYY-MM-DD)
   * @example "2025-01-01"
   */
  @ApiProperty({
    description: 'Lọc theo ngày bắt đầu (định dạng: YYYY-MM-DD)',
    example: '2025-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  /**
   * Filter by end date (format: YYYY-MM-DD)
   * @example "2025-12-31"
   */
  @ApiProperty({
    description: 'Lọc theo ngày kết thúc (định dạng: YYYY-MM-DD)',
    example: '2025-12-31',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;
}
