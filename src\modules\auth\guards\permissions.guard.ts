import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AppException, ErrorCode } from '../../../common/exceptions/app.exception';

export const PERMISSIONS_KEY = 'permissions';

@Injectable()
export class PermissionsGuard implements CanActivate {
  private readonly logger = new Logger(PermissionsGuard.name);

  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    // L<PERSON>y danh sách quyền cần thiết từ metadata
    const requiredPermissions = this.reflector.getAllAndOverride<string[]>(
      PERMISSIONS_KEY,
      [context.getHandler(), context.getClass()],
    );

    // Nếu không có yêu cầu về permissions, cho phép truy cập
    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const { user } = request;

    // <PERSON>ểm tra user đã đăng nhập chưa
    if (!user) {
      throw new AppException(
        ErrorCode.UNAUTHORIZED_ACCESS,
        'Bạn cần đăng nhập để thực hiện hành động này'
      );
    }

    // Nếu user là COMPANY_ADMIN, cho phép truy cập mà không cần kiểm tra permissions
    if (user.type === 'COMPANY_ADMIN') {
      this.logger.log(`User ${user.id} là COMPANY_ADMIN, tự động cấp quyền truy cập`);
      return true;
    }

    // Kiểm tra user có permissions không
    if (!user.permissions || !Array.isArray(user.permissions)) {
      throw new AppException(
        ErrorCode.FORBIDDEN,
        'Tài khoản của bạn không có quyền hạn nào'
      );
    }

    // Kiểm tra xem user có tất cả các permissions cần thiết không
    const missingPermissions = requiredPermissions.filter(
      permission => !user.permissions.includes(permission)
    );

    if (missingPermissions.length > 0) {
      throw new AppException(
        ErrorCode.FORBIDDEN,
        `Bạn không có đủ quyền để thực hiện hành động này. Quyền còn thiếu: ${missingPermissions.join(', ')}`
      );
    }

    return true;
  }
}