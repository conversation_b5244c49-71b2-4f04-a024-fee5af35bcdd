import { Controller, Get, Param, ParseIntPipe, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { DepartmentMembersService } from '../services/department-members.service';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { DepartmentMembersResponseDto } from '../dto/department/department-members.dto';

/**
 * Controller quản lý thành viên phòng ban
 */
@ApiTags(SWAGGER_API_TAG.EMPLOYEE)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('api/hrm/departments')
export class DepartmentMembersController {
  constructor(private readonly departmentMembersService: DepartmentMembersService) {}

  /**
   * Lấy danh sách thành viên thuộc phòng ban
   * @param user Người dùng hiện tại
   * @param departmentId ID của phòng ban
   * @returns Danh sách thành viên trong phòng ban
   */
  @Get(':id/members')
  @ApiOperation({ summary: 'Lấy danh sách thành viên phòng ban' })
  @ApiParam({ name: 'id', description: 'ID của phòng ban' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách thành viên phòng ban.',
    type: () => ApiResponseDto,
  })
  async getDepartmentMembers(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) departmentId: number,
  ): Promise<ApiResponseDto<DepartmentMembersResponseDto>> {
    const result = await this.departmentMembersService.getDepartmentMembers(departmentId);
    return ApiResponseDto.success(result);
  }
} 