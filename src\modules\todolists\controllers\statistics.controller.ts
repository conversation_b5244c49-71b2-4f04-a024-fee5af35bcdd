import {
  <PERSON>,
  Get,
  Param,
  ParseIntPipe,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { StatisticsService } from '../services/statistics.service';
import { UserPerformanceResponseDto } from '../dto/statistics/user-performance-response.dto';
import { ProjectPerformanceResponseDto } from '../dto/statistics/project-performance-response.dto';
import { ProjectGanttResponseDto } from '../dto/statistics/project-gantt-response.dto';

/**
 * Controller xử lý các API thống kê
 */
@ApiTags(SWAGGER_API_TAG.TODOLISTS)
@ApiExtraModels(
  ApiResponseDto,
  UserPerformanceResponseDto,
  ProjectPerformanceResponseDto,
  ProjectGanttResponseDto,
)
@Controller('api/statistics')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class StatisticsController {
  constructor(private readonly statisticsService: StatisticsService) {}

  /**
   * Lấy thống kê hiệu suất của người dùng
   */
  @Get('users/:userId/performance')
  @ApiOperation({ summary: 'Lấy thống kê hiệu suất của người dùng' })
  @ApiParam({ name: 'userId', description: 'ID người dùng', type: 'number' })
  @ApiQuery({ name: 'startDate', description: 'Ngày bắt đầu (timestamp)', required: false, type: 'number' })
  @ApiQuery({ name: 'endDate', description: 'Ngày kết thúc (timestamp)', required: false, type: 'number' })
  @ApiQuery({ name: 'projectId', description: 'ID dự án', required: false, type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Thống kê hiệu suất của người dùng',
    schema: ApiResponseDto.getSchema(UserPerformanceResponseDto),
  })
  async getUserPerformance(
    @Param('userId', ParseIntPipe) userId: number,
    @Query('startDate') startDate?: number,
    @Query('endDate') endDate?: number,
    @Query('projectId') projectId?: number,
  ) {
    const performance = await this.statisticsService.getUserPerformance(
      userId,
      startDate,
      endDate,
      projectId,
    );
    return ApiResponseDto.success(performance);
  }

  /**
   * Lấy thống kê hiệu suất của dự án
   */
  @Get('projects/:projectId/performance')
  @ApiOperation({ summary: 'Lấy thống kê hiệu suất của dự án' })
  @ApiParam({ name: 'projectId', description: 'ID dự án', type: 'number' })
  @ApiQuery({ name: 'startDate', description: 'Ngày bắt đầu (timestamp)', required: false, type: 'number' })
  @ApiQuery({ name: 'endDate', description: 'Ngày kết thúc (timestamp)', required: false, type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Thống kê hiệu suất của dự án',
    schema: ApiResponseDto.getSchema(ProjectPerformanceResponseDto),
  })
  async getProjectPerformance(
    @Param('projectId', ParseIntPipe) projectId: number,
    @Query('startDate') startDate?: number,
    @Query('endDate') endDate?: number,
  ) {
    const performance = await this.statisticsService.getProjectPerformance(
      projectId,
      startDate,
      endDate,
    );
    return ApiResponseDto.success(performance);
  }

  /**
   * Lấy dữ liệu biểu đồ Gantt cho dự án
   */
  @Get('projects/:projectId/gantt')
  @ApiOperation({ summary: 'Lấy dữ liệu biểu đồ Gantt cho dự án' })
  @ApiParam({ name: 'projectId', description: 'ID dự án', type: 'number' })
  @ApiQuery({ name: 'startDate', description: 'Ngày bắt đầu (timestamp)', required: false, type: 'number' })
  @ApiQuery({ name: 'endDate', description: 'Ngày kết thúc (timestamp)', required: false, type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Dữ liệu biểu đồ Gantt cho dự án',
    schema: ApiResponseDto.getSchema(ProjectGanttResponseDto),
  })
  async getProjectGanttChart(
    @Param('projectId', ParseIntPipe) projectId: number,
    @Query('startDate') startDate?: number,
    @Query('endDate') endDate?: number,
  ) {
    const ganttData = await this.statisticsService.getProjectGanttChart(
      projectId,
      startDate,
      endDate,
    );
    return ApiResponseDto.success(ganttData);
  }
}
