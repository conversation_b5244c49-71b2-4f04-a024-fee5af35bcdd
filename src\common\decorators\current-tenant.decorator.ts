import { createParamDecorator, ExecutionContext, HttpException, HttpStatus } from '@nestjs/common';
import { RequestWithUser } from '../middlewares/tenant-security.middleware';
import { AppException, ErrorCode } from '@/common';

/**
 * Decorator để lấy tenantId từ request
 * Sử dụng trong controller để lấy tenantId của người dùng hiện tại
 *
 * @example
 * @Get()
 * findAll(@CurrentTenant() tenantId: number) {
 *   return this.service.findAll(tenantId);
 * }
 */
export const CurrentTenant = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): number => {
    const request = ctx.switchToHttp().getRequest<RequestWithUser>();

    // Lấy tenantId từ request
    const tenantId = request.tenantId;

    if (!tenantId) {
      throw new AppException(
        ErrorCode.UNAUTHORIZED_ACCESS,
        'TenantId không tồn tại trong request. Đ<PERSON><PERSON> bảo rằng TenantSecurityMiddleware đã được áp dụng.'
      );
    }

    return tenantId;
  },
);
