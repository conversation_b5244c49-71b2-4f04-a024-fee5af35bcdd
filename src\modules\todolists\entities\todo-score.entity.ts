import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Entity representing todo scores (evaluations of completed tasks)
 */
@Entity('todo_scores')
export class TodoScore {
  /**
   * Unique identifier for the todo score
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID of the todo being scored
   */
  @Column({ name: 'todo_id', type: 'integer', nullable: false })
  todoId: number;

  /**
   * ID of the user who scored the todo
   */
  @Column({ name: 'scorer_id', type: 'integer', nullable: false })
  scorerId: number;

  /**
   * Stars awarded to the todo (1-5)
   */
  @Column({ name: 'awarded_stars', type: 'integer', nullable: false })
  awardedStars: number;

  /**
   * Feedback or comments about the todo
   */
  @Column({ type: 'text', nullable: true })
  feedback: string | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * ID of the company/organization that owns this record
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
