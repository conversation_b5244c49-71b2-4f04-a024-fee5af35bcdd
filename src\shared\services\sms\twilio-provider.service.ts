import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BaseSmsProvider } from './base-sms-provider.service';
import { SmsResponse, BulkSmsResponse, MessageStatusResponse, ConnectionTestResponse, MessageStatus } from './sms-provider.interface';

// Importer le client Twilio
let twilio;
try {
  twilio = require('twilio');
} catch (e) {
  // Twilio n'est pas installé, nous le gérerons dans le constructeur
}

/**
 * Interface pour la configuration de Twilio
 */
export interface TwilioConfig {
  /**
   * SID du compte Twilio
   */
  accountSid: string;

  /**
   * Token d'authentification Twilio
   */
  authToken: string;

  /**
   * Numéro de téléphone ou service de messagerie Twilio à utiliser comme expéditeur
   */
  from?: string;

  /**
   * SID du service de messagerie Twilio (alternative à from)
   */
  messagingServiceSid?: string;
}

/**
 * Service d'intégration avec l'API Twilio
 */
@Injectable()
export class TwilioProvider extends BaseSmsProvider {
  readonly providerName = 'Twilio';
  
  private client: any;
  private readonly accountSid: string;
  private readonly authToken: string;
  private readonly defaultFrom: string;
  private readonly defaultMessagingServiceSid: string;

  constructor(private readonly configService: ConfigService) {
    super('TwilioProvider');
    
    // Charger la configuration depuis les variables d'environnement ou utiliser des valeurs par défaut
    this.accountSid = this.configService.get<string>('TWILIO_ACCOUNT_SID') || '';
    this.authToken = this.configService.get<string>('TWILIO_AUTH_TOKEN') || '';
    this.defaultFrom = this.configService.get<string>('TWILIO_PHONE_NUMBER') || '';
    this.defaultMessagingServiceSid = this.configService.get<string>('TWILIO_MESSAGING_SERVICE_SID') || '';
    
    // Vérifier si Twilio est installé
    if (!twilio) {
      this.logger.warn('Le package "twilio" n\'est pas installé. Veuillez l\'installer avec "npm install twilio"');
    } else {
      // Initialiser le client Twilio
      this.initClient(this.accountSid, this.authToken);
    }
  }

  /**
   * Initialise le client Twilio avec les identifiants fournis
   * @param accountSid SID du compte Twilio
   * @param authToken Token d'authentification Twilio
   */
  private initClient(accountSid: string, authToken: string): void {
    if (twilio && accountSid && authToken) {
      try {
        this.client = twilio(accountSid, authToken);
        this.logger.log('Client Twilio initialisé avec succès');
      } catch (error) {
        this.logger.error(`Erreur lors de l'initialisation du client Twilio: ${error.message}`, error.stack);
      }
    }
  }

  /**
   * Envoie un SMS à un numéro de téléphone via Twilio
   * @param phoneNumber Numéro de téléphone du destinataire
   * @param message Contenu du message
   * @param options Options supplémentaires (from, messagingServiceSid)
   * @returns Promesse contenant l'ID du message ou une erreur
   */
  async sendSms(phoneNumber: string, message: string, options?: any): Promise<SmsResponse> {
    try {
      this.logger.debug(`Envoi d'un SMS à ${phoneNumber} via Twilio`);
      
      // Vérifier si le client Twilio est initialisé
      if (!this.client) {
        throw new Error('Le client Twilio n\'est pas initialisé');
      }
      
      const formattedPhoneNumber = this.formatPhoneNumber(phoneNumber);
      
      // Préparer les paramètres pour l'envoi du SMS
      const params: any = {
        to: formattedPhoneNumber,
        body: message
      };
      
      // Utiliser le service de messagerie ou le numéro d'expéditeur
      if (options?.messagingServiceSid) {
        params.messagingServiceSid = options.messagingServiceSid;
      } else if (options?.from) {
        params.from = options.from;
      } else if (this.defaultMessagingServiceSid) {
        params.messagingServiceSid = this.defaultMessagingServiceSid;
      } else if (this.defaultFrom) {
        params.from = this.defaultFrom;
      } else {
        throw new Error('Aucun expéditeur spécifié (from ou messagingServiceSid)');
      }
      
      // Ajouter les options supplémentaires
      if (options?.statusCallback) {
        params.statusCallback = options.statusCallback;
      }
      
      // Envoyer le SMS via Twilio
      const twilioMessage = await this.client.messages.create(params);
      
      return {
        success: true,
        messageId: twilioMessage.sid,
        rawResponse: twilioMessage
      };
    } catch (error) {
      this.logger.error(`Exception lors de l'envoi du SMS via Twilio: ${error.message}`, error.stack);
      return {
        success: false,
        errorCode: error.code,
        errorMessage: error.message || 'Erreur inconnue'
      };
    }
  }

  /**
   * Vérifie le statut d'un message envoyé via Twilio
   * @param messageId SID du message Twilio à vérifier
   * @returns Promesse contenant le statut du message
   */
  async checkMessageStatus(messageId: string): Promise<MessageStatusResponse> {
    try {
      this.logger.debug(`Vérification du statut du message ${messageId} via Twilio`);
      
      // Vérifier si le client Twilio est initialisé
      if (!this.client) {
        throw new Error('Le client Twilio n\'est pas initialisé');
      }
      
      // Récupérer le message depuis Twilio
      const twilioMessage = await this.client.messages(messageId).fetch();
      
      return {
        messageId,
        status: this.mapTwilioStatus(twilioMessage.status),
        updatedAt: new Date(twilioMessage.dateUpdated),
        details: twilioMessage.errorMessage || '',
        rawResponse: twilioMessage
      };
    } catch (error) {
      this.logger.error(`Exception lors de la vérification du statut du message via Twilio: ${error.message}`, error.stack);
      return {
        messageId,
        status: MessageStatus.UNKNOWN,
        updatedAt: new Date(),
        details: error.message || 'Erreur inconnue'
      };
    }
  }

  /**
   * Envoie un SMS avec un brandname via Twilio
   * @param phoneNumber Numéro de téléphone du destinataire
   * @param message Contenu du message
   * @param brandname Nom de la marque à utiliser comme expéditeur (doit être un numéro Twilio valide)
   * @param options Options supplémentaires
   * @returns Promesse contenant l'ID du message ou une erreur
   */
  async sendBrandnameSms(phoneNumber: string, message: string, brandname: string, options?: any): Promise<SmsResponse> {
    // Pour Twilio, le brandname doit être un numéro de téléphone valide ou un alphasender
    return this.sendSms(phoneNumber, message, { 
      ...options, 
      from: brandname 
    });
  }

  /**
   * Teste la connexion avec Twilio
   * @param config Configuration de Twilio
   * @returns Promesse indiquant si la connexion est réussie
   */
  async testConnection(config: TwilioConfig): Promise<ConnectionTestResponse> {
    try {
      this.logger.debug('Test de connexion avec Twilio');
      
      const accountSid = config.accountSid || this.accountSid;
      const authToken = config.authToken || this.authToken;
      
      if (!accountSid || !authToken) {
        return {
          success: false,
          message: 'Identifiants Twilio manquants'
        };
      }
      
      // Créer un client temporaire pour le test
      const testClient = twilio(accountSid, authToken);
      
      // Tester la connexion en récupérant les informations du compte
      const account = await testClient.api.accounts(accountSid).fetch();
      
      return {
        success: true,
        message: 'Connexion réussie',
        details: {
          friendlyName: account.friendlyName,
          status: account.status,
          type: account.type
        }
      };
    } catch (error) {
      this.logger.error(`Exception lors du test de connexion avec Twilio: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Erreur inconnue'
      };
    }
  }

  /**
   * Convertit un statut Twilio en statut standard
   * @param twilioStatus Statut Twilio
   * @returns Statut standard
   */
  private mapTwilioStatus(twilioStatus: string): MessageStatus {
    switch (twilioStatus) {
      case 'queued':
        return MessageStatus.PENDING;
      case 'sending':
      case 'sent':
        return MessageStatus.SENDING;
      case 'delivered':
        return MessageStatus.DELIVERED;
      case 'undelivered':
      case 'failed':
        return MessageStatus.FAILED;
      default:
        return MessageStatus.UNKNOWN;
    }
  }
}
