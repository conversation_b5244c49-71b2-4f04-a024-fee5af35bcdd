import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { AppModule } from '../../../app.module';

/**
 * Script để kiểm tra kết nối database
 */
async function bootstrap() {
  const logger = new Logger('DatabaseConnectionTest');
  
  try {
    logger.log('Starting database connection test...');
    
    // Tạo ứng dụng NestJS
    const app = await NestFactory.create(AppModule);
    
    // Lấy DataSource từ ứng dụng
    const dataSource = app.get(DataSource);
    
    // Kiểm tra DataSource đã được khởi tạo
    if (dataSource && dataSource.isInitialized) {
      logger.log('Database connection initialized successfully');
      
      // Thực hiện truy vấn đơn giản để kiểm tra kết nối
      const result = await dataSource.query('SELECT 1 as value');
      logger.log(`Query result: ${JSON.stringify(result)}`);
      
      // Lấy thông tin về các entity đã đăng ký
      const entityCount = dataSource.entityMetadatas.length;
      logger.log(`Registered entities: ${entityCount}`);
      
      // Liệt kê tên các entity
      const entityNames = dataSource.entityMetadatas.map(metadata => metadata.name);
      logger.log(`Entity names: ${entityNames.join(', ')}`);
      
      logger.log('Database connection test completed successfully');
    } else {
      logger.error('Database connection failed: DataSource not initialized');
    }
    
    // Đóng ứng dụng
    await app.close();
    
  } catch (error) {
    logger.error(`Database connection test failed: ${error.message}`, error.stack);
  }
}

// Chạy script
bootstrap();
