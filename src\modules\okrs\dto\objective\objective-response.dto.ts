import { ApiProperty } from '@nestjs/swagger';
import { ObjectiveType } from '../../enum/objective-type.enum';

/**
 * DTO for objective response
 */
export class ObjectiveResponseDto {
  /**
   * Unique identifier for the objective
   * @example 1
   */
  @ApiProperty({
    description: 'ID của mục tiêu',
    example: 1,
  })
  id: number;

  /**
   * Title of the objective
   * @example "Tăng doanh thu 20%"
   */
  @ApiProperty({
    description: 'Tiêu đề mục tiêu',
    example: 'Tăng doanh thu 20%',
  })
  title: string;

  /**
   * Detailed description of the objective
   * @example "Tăng doanh thu 20% so với quý trước thông qua các chiến dịch marketing mới"
   */
  @ApiProperty({
    description: 'Mô tả chi tiết mục tiêu',
    example: 'Tăng doanh thu 20% so với quý trước thông qua các chiến dịch marketing mới',
    nullable: true,
  })
  description: string | null;

  /**
   * ID of the user responsible for the objective
   * @example 1
   */
  @ApiProperty({
    description: 'ID của người chịu trách nhiệm',
    example: 1,
  })
  ownerId: number;

  /**
   * ID of the department (if applicable)
   * @example 2
   */
  @ApiProperty({
    description: 'ID của phòng ban (nếu có)',
    example: 2,
    nullable: true,
  })
  departmentId: number | null;

  /**
   * ID of the parent objective (if this is a child objective)
   * @example 3
   */
  @ApiProperty({
    description: 'ID của mục tiêu cha (nếu là mục tiêu con)',
    example: 3,
    nullable: true,
  })
  parentId: number | null;

  /**
   * ID of the OKR cycle
   * @example 1
   */
  @ApiProperty({
    description: 'ID của chu kỳ OKR',
    example: 1,
  })
  cycleId: number | null;

  /**
   * Type of the objective
   * @example "COMPANY"
   */
  @ApiProperty({
    description: 'Loại mục tiêu',
    enum: ObjectiveType,
    example: ObjectiveType.COMPANY,
  })
  type: ObjectiveType;

  /**
   * Objective completion progress (percentage)
   * @example 75
   */
  @ApiProperty({
    description: 'Tiến độ hoàn thành mục tiêu (phần trăm)',
    example: 75,
    nullable: true,
  })
  progress: number | null;

  /**
   * Status of the objective
   * @example "active"
   */
  @ApiProperty({
    description: 'Trạng thái mục tiêu',
    example: 'active',
    nullable: true,
  })
  status: string | null;

  /**
   * Start date of the objective
   * @example "2025-01-01"
   */
  @ApiProperty({
    description: 'Ngày bắt đầu mục tiêu',
    example: '2025-01-01',
  })
  startDate: string;

  /**
   * End date of the objective
   * @example "2025-03-31"
   */
  @ApiProperty({
    description: 'Ngày kết thúc mục tiêu',
    example: '2025-03-31',
  })
  endDate: string;

  /**
   * ID of the user who created the objective
   * @example 1
   */
  @ApiProperty({
    description: 'ID của người tạo mục tiêu',
    example: 1,
  })
  createdBy: number;

  /**
   * Creation timestamp (in milliseconds)
   * @example 1672531200000
   */
  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1672531200000,
    nullable: true,
  })
  createdAt: number | null;

  /**
   * Last update timestamp (in milliseconds)
   * @example 1672617600000
   */
  @ApiProperty({
    description: 'Thời gian cập nhật gần nhất (timestamp)',
    example: 1672617600000,
    nullable: true,
  })
  updatedAt: number | null;
}
