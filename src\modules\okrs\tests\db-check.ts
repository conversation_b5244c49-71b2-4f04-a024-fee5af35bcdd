import { DataSource } from 'typeorm';
import { OkrCycle } from '../entities/okr-cycle.entity';

async function testConnection() {
  console.log('Creating DataSource...');
  const dataSource = new DataSource({
    type: 'postgres',
    host: 'localhost',
    port: 5432,
    username: 'postgres',
    password: 'postgres',
    database: 'redai_dev',
    entities: [OkrCycle],
    synchronize: false,
  });

  try {
    console.log('Initializing connection...');
    await dataSource.initialize();
    console.log('Connected successfully!');
    
    console.log('Executing query...');
    const result = await dataSource.query('SELECT 1 as value');
    console.log('Query result:', result);
    
    console.log('Checking OkrCycle entity...');
    const okrCycleRepository = dataSource.getRepository(OkrCycle);
    const count = await okrCycleRepository.count();
    console.log(`Found ${count} OkrCycle records`);
    
    await dataSource.destroy();
    console.log('Connection closed.');
  } catch (error) {
    console.error('Error:', error);
  }
}

testConnection();
