import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString, IsUrl } from 'class-validator';
import { SocialProvider } from '../enum';

/**
 * DTO cơ sở cho đăng nhập mạng xã hội
 */
export class SocialAuthBaseDto {
  @ApiProperty({
    description: 'Access token từ nhà cung cấp mạng xã hội',
    example: '**********************...',
  })
  @IsNotEmpty({ message: 'Access token không được để trống' })
  @IsString({ message: 'Access token phải là chuỗi' })
  accessToken: string;

  @ApiProperty({
    description: 'Loại nhà cung cấp mạng xã hội',
    enum: SocialProvider,
    example: SocialProvider.GOOGLE,
  })
  @IsNotEmpty({ message: 'Loại nhà cung cấp không được để trống' })
  @IsString({ message: 'Loạ<PERSON> nhà cung cấp phải là chuỗi' })
  provider: SocialProvider;
}

/**
 * DTO cho đăng nhập bằng mạng xã hội
 */
export class SocialLoginDto extends SocialAuthBaseDto {}

/**
 * DTO cho đăng ký bằng mạng xã hội
 */
export class SocialRegisterDto extends SocialAuthBaseDto {
  @ApiProperty({
    description: 'Tên đăng nhập (tùy chọn, sẽ được tạo tự động nếu không cung cấp)',
    example: 'johndoe',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên đăng nhập phải là chuỗi' })
  username?: string;

  @ApiProperty({
    description: 'Email (tùy chọn, sẽ lấy từ thông tin mạng xã hội nếu không cung cấp)',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail({}, { message: 'Email không hợp lệ' })
  email?: string;

  @ApiProperty({
    description: 'Họ tên đầy đủ (tùy chọn, sẽ lấy từ thông tin mạng xã hội nếu không cung cấp)',
    example: 'John Doe',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Họ tên phải là chuỗi' })
  fullName?: string;

  @ApiProperty({
    description: 'URL ảnh đại diện (tùy chọn, sẽ lấy từ thông tin mạng xã hội nếu không cung cấp)',
    example: 'https://example.com/avatar.jpg',
    required: false,
  })
  @IsOptional()
  @IsUrl({}, { message: 'URL ảnh đại diện không hợp lệ' })
  avatarUrl?: string;
}

/**
 * DTO cho phản hồi đăng nhập/đăng ký bằng mạng xã hội
 */
export class SocialAuthResponseDto {
  @ApiProperty({
    description: 'Token truy cập',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: 'Thông tin người dùng',
  })
  user: any;

  @ApiProperty({
    description: 'Danh sách quyền của người dùng',
    example: ['user:view', 'user:create', 'project:view'],
    type: [String],
  })
  permissions: string[];

  @ApiProperty({
    description: 'Nhà cung cấp mạng xã hội đã sử dụng để đăng nhập',
    enum: SocialProvider,
    example: SocialProvider.GOOGLE,
  })
  provider: SocialProvider;

  @ApiProperty({
    description: 'ID của người dùng trên mạng xã hội',
    example: '123456789012345678901',
  })
  socialId: string;
}
