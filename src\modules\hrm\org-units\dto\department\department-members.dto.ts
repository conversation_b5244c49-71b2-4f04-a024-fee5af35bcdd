import { ApiProperty } from '@nestjs/swagger';
import { UserStatus } from '@/modules/auth/enum/user-status.enum';

/**
 * DTO đại diện cho thông tin thành viên trong phòng ban
 */
export class DepartmentMemberDto {
  /**
   * ID của người dùng
   */
  @ApiProperty({
    description: 'ID của người dùng',
    example: 1,
  })
  id: number;

  /**
   * Tên đầy đủ của người dùng
   */
  @ApiProperty({
    description: 'Họ tên đầy đủ của người dùng',
    example: 'Nguyễn Văn A',
    nullable: true,
  })
  fullName: string | null;

  /**
   * Email của người dùng
   */
  @ApiProperty({
    description: 'Email của người dùng',
    example: '<EMAIL>',
  })
  email: string;

  /**
   * Vị trí công việc của người dùng
   */
  @ApiProperty({
    description: 'Vị trí công việc của người dùng',
    example: 'Nhà phát triển',
    nullable: true,
  })
  position: string | null;

  /**
   * Số điện thoại của người dùng
   */
  @ApiProperty({
    description: 'Số điện thoại của người dùng',
    example: '0987654321',
    nullable: true,
  })
  phoneNumber: string | null;

  /**
   * URL avatar của người dùng
   */
  @ApiProperty({
    description: 'URL avatar của người dùng',
    example: 'https://example.com/avatar.jpg',
    nullable: true,
  })
  avatarUrl: string | null;

  /**
   * Trạng thái của người dùng
   */
  @ApiProperty({
    description: 'Trạng thái của người dùng',
    enum: UserStatus,
    example: UserStatus.ACTIVE,
    nullable: true,
  })
  status: UserStatus | null;

  /**
   * Người dùng có phải là trưởng phòng không
   */
  @ApiProperty({
    description: 'Người dùng có phải là trưởng phòng không',
    example: true,
  })
  isManager: boolean;
}

/**
 * DTO đại diện cho danh sách thành viên phòng ban và thông tin tổng hợp
 */
export class DepartmentMembersResponseDto {
  /**
   * Thông tin phòng ban
   */
  @ApiProperty({
    description: 'Thông tin phòng ban',
    example: {
      id: 1,
      name: 'Phòng Kỹ thuật',
    },
  })
  department: {
    id: number;
    name: string;
    managerId: number | null;
  };

  /**
   * Danh sách thành viên
   */
  @ApiProperty({
    description: 'Danh sách thành viên trong phòng ban',
    type: [DepartmentMemberDto],
  })
  members: DepartmentMemberDto[];

  /**
   * Tổng số thành viên trong phòng ban
   */
  @ApiProperty({
    description: 'Tổng số thành viên trong phòng ban',
    example: 5,
  })
  totalMembers: number;
} 