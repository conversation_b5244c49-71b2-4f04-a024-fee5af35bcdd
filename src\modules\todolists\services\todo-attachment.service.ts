import { Injectable, Logger } from '@nestjs/common';
import { TodoAttachmentRepository } from '../repositories/todo-attachment.repository';
import { TodoRepository } from '../repositories/todo.repository';
import { TodoAttachment } from '../entities/todo-attachment.entity';
import { CreateTodoAttachmentDto } from '../dto/todo-attachment/create-todo-attachment.dto';
import { TodoAttachmentQueryDto } from '../dto/todo-attachment/todo-attachment-query.dto';
import { TodoAttachmentResponseDto } from '../dto/todo-attachment/todo-attachment-response.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common/exceptions/app.exception';
import { TODOLISTS_ERROR_CODES } from '../errors/todolists-error.code';

/**
 * Service xử lý logic nghiệp vụ cho tệp đ<PERSON>h kèm công việc
 */
@Injectable()
export class TodoAttachmentService {
  private readonly logger = new Logger(TodoAttachmentService.name);

  constructor(
    private readonly todoAttachmentRepository: TodoAttachmentRepository,
    private readonly todoRepository: TodoRepository,
  ) {}

  /**
   * Chuyển đổi entity sang DTO
   * @param attachment Entity tệp đính kèm
   * @returns DTO tệp đính kèm
   */
  private mapToDto(attachment: TodoAttachment): TodoAttachmentResponseDto {
    return {
      id: attachment.id,
      todoId: attachment.todoId,
      filename: attachment.filename,
      url: attachment.url,
      contentType: attachment.contentType,
      size: attachment.size,
      createdAt: attachment.createdAt,
      createdBy: attachment.createdBy,
    };
  }

  /**
   * Thêm tệp đính kèm cho công việc
   * @param currentUserId ID người dùng hiện tại
   * @param dto Dữ liệu tệp đính kèm
   * @returns Thông tin tệp đính kèm đã thêm
   */
  async addAttachment(
    currentUserId: number,
    dto: CreateTodoAttachmentDto,
  ): Promise<TodoAttachmentResponseDto> {
    try {
      // Kiểm tra công việc tồn tại
      const todo = await this.todoRepository.findById(dto.todoId);
      if (!todo) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
          `Không tìm thấy công việc với ID ${dto.todoId}`,
        );
      }

      // Tạo tệp đính kèm mới
      const now = Date.now();
      const attachment = await this.todoAttachmentRepository.create({
        todoId: dto.todoId,
        filename: dto.filename,
        url: dto.url,
        contentType: dto.contentType,
        size: dto.size,
        createdAt: now,
        createdBy: currentUserId,
      });

      return this.mapToDto(attachment);
    } catch (error) {
      this.logger.error(`Lỗi khi thêm tệp đính kèm: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TODOLISTS_ERROR_CODES.ATTACHMENT_CREATION_FAILED,
        'Không thể thêm tệp đính kèm',
      );
    }
  }

  /**
   * Lấy danh sách tệp đính kèm
   * @param query Tham số truy vấn
   * @returns Danh sách tệp đính kèm đã phân trang
   */
  async findAll(
    query: TodoAttachmentQueryDto,
  ): Promise<PaginatedResult<TodoAttachmentResponseDto>> {
    try {
      const paginatedResult = await this.todoAttachmentRepository.findAll(query);

      return {
        items: paginatedResult.items.map(attachment => this.mapToDto(attachment)),
        meta: paginatedResult.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách tệp đính kèm: ${error.message}`, error.stack);
      throw new AppException(
        TODOLISTS_ERROR_CODES.ATTACHMENT_FETCH_FAILED,
        'Không thể lấy danh sách tệp đính kèm',
      );
    }
  }

  /**
   * Lấy danh sách tệp đính kèm của một công việc
   * @param todoId ID công việc
   * @returns Danh sách tệp đính kèm
   */
  async findByTodoId(todoId: number): Promise<TodoAttachmentResponseDto[]> {
    try {
      const attachments = await this.todoAttachmentRepository.findByTodoId(todoId);
      return attachments.map(attachment => this.mapToDto(attachment));
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách tệp đính kèm của công việc: ${error.message}`, error.stack);
      throw new AppException(
        TODOLISTS_ERROR_CODES.ATTACHMENT_FETCH_FAILED,
        'Không thể lấy danh sách tệp đính kèm của công việc',
      );
    }
  }

  /**
   * Lấy chi tiết tệp đính kèm
   * @param id ID tệp đính kèm
   * @returns Thông tin chi tiết tệp đính kèm
   */
  async findById(id: number): Promise<TodoAttachmentResponseDto> {
    try {
      const attachment = await this.todoAttachmentRepository.findById(id);
      if (!attachment) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.ATTACHMENT_NOT_FOUND,
          `Không tìm thấy tệp đính kèm với ID ${id}`,
        );
      }
      return this.mapToDto(attachment);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết tệp đính kèm: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TODOLISTS_ERROR_CODES.ATTACHMENT_FETCH_FAILED,
        'Không thể lấy chi tiết tệp đính kèm',
      );
    }
  }

  /**
   * Xóa tệp đính kèm
   * @param currentUserId ID người dùng hiện tại
   * @param id ID tệp đính kèm
   */
  async removeAttachment(currentUserId: number, id: number): Promise<void> {
    try {
      // Kiểm tra tệp đính kèm tồn tại
      const attachment = await this.todoAttachmentRepository.findById(id);
      if (!attachment) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.ATTACHMENT_NOT_FOUND,
          `Không tìm thấy tệp đính kèm với ID ${id}`,
        );
      }

      // Kiểm tra quyền xóa tệp đính kèm
      if (attachment.createdBy !== currentUserId) {
        // Kiểm tra xem người dùng có phải là người tạo công việc hoặc người được giao việc không
        const todo = await this.todoRepository.findById(attachment.todoId);
        if (!todo || (todo.createdBy !== currentUserId && todo.assigneeId !== currentUserId)) {
          throw new AppException(
            TODOLISTS_ERROR_CODES.NOT_ATTACHMENT_OWNER,
            'Bạn không có quyền xóa tệp đính kèm này',
          );
        }
      }

      // Xóa tệp đính kèm
      await this.todoAttachmentRepository.remove(id);

      // TODO: Xóa tệp thực tế trên storage (S3, CloudFlare, ...)
      // Có thể thêm logic xóa tệp thực tế ở đây
    } catch (error) {
      this.logger.error(`Lỗi khi xóa tệp đính kèm: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TODOLISTS_ERROR_CODES.ATTACHMENT_DELETION_FAILED,
        'Không thể xóa tệp đính kèm',
      );
    }
  }
}
