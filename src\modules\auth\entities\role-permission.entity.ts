import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Entity representing the link between roles and permissions
 */
@Entity('role_permissions')
export class RolePermission {
  /**
   * Unique identifier for the link
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID of the role
   */
  @Column({ name: 'role_id', type: 'integer', nullable: true })
  roleId: number | null;

  /**
   * ID of the permission
   */
  @Column({ name: 'permission_id', type: 'integer', nullable: true })
  permissionId: number | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * ID of the company/organization that owns this record
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
