import { ApiProperty } from '@nestjs/swagger';
import { ProjectMemberRole } from '../../enum/project-member-role.enum';

/**
 * DTO cho thông tin hiệu suất thành viên dự án
 */
export class MemberPerformanceDto {
  /**
   * ID người dùng
   * @example 1
   */
  @ApiProperty({
    description: 'ID người dùng',
    example: 1,
  })
  userId: number;

  /**
   * Vai trò trong dự án
   * @example "admin"
   */
  @ApiProperty({
    description: 'Vai trò trong dự án',
    enum: ProjectMemberRole,
    example: ProjectMemberRole.ADMIN,
  })
  role: ProjectMemberRole;

  /**
   * Tổng số công việc
   * @example 8
   */
  @ApiProperty({
    description: 'Tổng số công việc',
    example: 8,
  })
  totalTasks: number;

  /**
   * Số công việc đã hoàn thành
   * @example 5
   */
  @ApiProperty({
    description: '<PERSON>ố công việc đã hoàn thành',
    example: 5,
  })
  completedTasks: number;

  /**
   * Điểm trung bình
   * @example 4.5
   */
  @ApiProperty({
    description: 'Điểm trung bình',
    example: 4.5,
  })
  averageScore: number;

  /**
   * Tỷ lệ hoàn thành (%)
   * @example 62.5
   */
  @ApiProperty({
    description: 'Tỷ lệ hoàn thành (%)',
    example: 62.5,
  })
  completionRate: number;
}

/**
 * DTO cho phản hồi thông tin hiệu suất dự án
 */
export class ProjectPerformanceResponseDto {
  /**
   * Thông tin dự án
   */
  @ApiProperty({
    description: 'Thông tin dự án',
    example: {
      id: 1,
      name: 'Dự án A',
      description: 'Mô tả dự án A',
      isActive: true,
    },
  })
  project: {
    id: number;
    name: string;
    description: string | null;
    isActive: boolean;
  };

  /**
   * Tổng số công việc
   * @example 25
   */
  @ApiProperty({
    description: 'Tổng số công việc',
    example: 25,
  })
  totalTasks: number;

  /**
   * Số công việc đã hoàn thành
   * @example 15
   */
  @ApiProperty({
    description: 'Số công việc đã hoàn thành',
    example: 15,
  })
  completedTasks: number;

  /**
   * Số công việc đang thực hiện
   * @example 7
   */
  @ApiProperty({
    description: 'Số công việc đang thực hiện',
    example: 7,
  })
  inProgressTasks: number;

  /**
   * Số công việc đang chờ
   * @example 3
   */
  @ApiProperty({
    description: 'Số công việc đang chờ',
    example: 3,
  })
  pendingTasks: number;

  /**
   * Điểm trung bình
   * @example 4.3
   */
  @ApiProperty({
    description: 'Điểm trung bình',
    example: 4.3,
  })
  averageScore: number;

  /**
   * Tỷ lệ hoàn thành (%)
   * @example 60
   */
  @ApiProperty({
    description: 'Tỷ lệ hoàn thành (%)',
    example: 60,
  })
  completionRate: number;

  /**
   * Thống kê chi tiết
   */
  @ApiProperty({
    description: 'Thống kê chi tiết',
    example: {
      byStatus: {
        completed: 15,
        inProgress: 7,
        pending: 3,
      },
      byScore: {
        averageScore: 4.3,
        totalScoredTasks: 12,
      },
    },
  })
  statistics: {
    byStatus: {
      completed: number;
      inProgress: number;
      pending: number;
    };
    byScore: {
      averageScore: number;
      totalScoredTasks: number;
    };
  };

  /**
   * Thông tin hiệu suất của các thành viên
   */
  @ApiProperty({
    description: 'Thông tin hiệu suất của các thành viên',
    type: [MemberPerformanceDto],
  })
  members: MemberPerformanceDto[];
}
