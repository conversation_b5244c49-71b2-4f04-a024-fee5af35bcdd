import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi thông tin hiệu suất người dùng
 */
export class UserPerformanceResponseDto {
  /**
   * Thông tin người dùng
   */
  @ApiProperty({
    description: 'Thông tin người dùng',
    example: {
      id: 1,
    },
  })
  user: {
    id: number;
  };

  /**
   * Tổng số công việc
   * @example 15
   */
  @ApiProperty({
    description: 'Tổng số công việc',
    example: 15,
  })
  totalTasks: number;

  /**
   * Số công việc đã hoàn thành
   * @example 10
   */
  @ApiProperty({
    description: 'Số công việc đã hoàn thành',
    example: 10,
  })
  completedTasks: number;

  /**
   * Số công việc đang thực hiện
   * @example 3
   */
  @ApiProperty({
    description: 'Số công việc đang thực hiện',
    example: 3,
  })
  inProgressTasks: number;

  /**
   * Số công việc đang chờ
   * @example 2
   */
  @ApiProperty({
    description: 'Số công việc đang chờ',
    example: 2,
  })
  pendingTasks: number;

  /**
   * Điểm trung bình
   * @example 4.2
   */
  @ApiProperty({
    description: 'Điểm trung bình',
    example: 4.2,
  })
  averageScore: number;

  /**
   * Tỷ lệ hoàn thành (%)
   * @example 66.67
   */
  @ApiProperty({
    description: 'Tỷ lệ hoàn thành (%)',
    example: 66.67,
  })
  completionRate: number;

  /**
   * Thống kê chi tiết
   */
  @ApiProperty({
    description: 'Thống kê chi tiết',
    example: {
      byStatus: {
        completed: 10,
        inProgress: 3,
        pending: 2,
      },
      byScore: {
        averageScore: 4.2,
        totalScoredTasks: 8,
      },
    },
  })
  statistics: {
    byStatus: {
      completed: number;
      inProgress: number;
      pending: number;
    };
    byScore: {
      averageScore: number;
      totalScoredTasks: number;
    };
  };
}
