import { Injectable, Logger } from '@nestjs/common';
import { DepartmentRepository } from '../repositories/department.repository';
import { UserRepository } from '@/modules/auth/repositories/user.repository';
import { DepartmentMemberDto, DepartmentMembersResponseDto } from '../dto/department/department-members.dto';
import { AppException } from '@/common';
import { HRM_ERROR_CODES } from '../../errors/hrm-error.code';

/**
 * Service quản lý thành viên phòng ban
 */
@Injectable()
export class DepartmentMembersService {
  private readonly logger = new Logger(DepartmentMembersService.name);

  constructor(
    private readonly departmentRepository: DepartmentRepository,
    private readonly userRepository: UserRepository,
  ) {}

  /**
   * Lấy danh sách thành viên thuộc một phòng ban
   * @param departmentId ID phòng ban
   * @returns Thông tin phòng ban và danh sách thành viên
   */
  async getDepartmentMembers(departmentId: number): Promise<DepartmentMembersResponseDto> {
    // Kiểm tra phòng ban tồn tại
    const department = await this.departmentRepository.findById(departmentId);
    if (!department) {
      throw new AppException(
        HRM_ERROR_CODES.DEPARTMENT_NOT_FOUND,
        `Không tìm thấy phòng ban với ID ${departmentId}`,
      );
    }

    // Lấy danh sách thành viên thuộc phòng ban
    const members = await this.userRepository.findByDepartmentId(departmentId);

    // Chuyển đổi dữ liệu sang DTO
    const memberDtos: DepartmentMemberDto[] = members.map(member => {
      const memberDto = new DepartmentMemberDto();
      memberDto.id = member.id;
      memberDto.fullName = member.fullName;
      memberDto.email = member.email;
      memberDto.position = member.position;
      memberDto.phoneNumber = member.phoneNumber;
      memberDto.avatarUrl = member.avatarUrl;
      memberDto.status = member.status;
      // Đánh dấu ai là trưởng phòng
      memberDto.isManager = department.managerId === member.id;
      
      return memberDto;
    });

    // Tạo response
    const response = new DepartmentMembersResponseDto();
    response.department = {
      id: department.id,
      name: department.name,
      managerId: department.managerId,
    };
    response.members = memberDtos;
    response.totalMembers = memberDtos.length;

    return response;
  }
} 