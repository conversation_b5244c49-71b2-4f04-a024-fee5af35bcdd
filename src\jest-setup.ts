// C<PERSON><PERSON> hình Jest để xử lý các thành phần NestJS
import { Logger } from '@nestjs/common';

// Tắt logging trong quá trình test
Logger.overrideLogger(false);

// Thiết lập timeout dà<PERSON> hơn cho các test
jest.setTimeout(30000);

// Xử lý các cảnh báo không cần thiết
global.console = {
  ...console,
  // Bỏ qua các log không cần thiết trong quá trình test
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};
