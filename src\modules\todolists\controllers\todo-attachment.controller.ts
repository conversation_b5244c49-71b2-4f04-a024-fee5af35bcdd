import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { TodoAttachmentService } from '../services/todo-attachment.service';
import { CreateTodoAttachmentDto } from '../dto/todo-attachment/create-todo-attachment.dto';
import { TodoAttachmentQueryDto } from '../dto/todo-attachment/todo-attachment-query.dto';
import { TodoAttachmentResponseDto } from '../dto/todo-attachment/todo-attachment-response.dto';

/**
 * Controller xử lý các API liên quan đến tệp đính kèm công việc
 */
@ApiTags(SWAGGER_API_TAG.TODOLISTS)
@ApiExtraModels(
  ApiResponseDto,
  TodoAttachmentResponseDto,
)
@Controller('api/v1/todo-attachments')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class TodoAttachmentController {
  constructor(
    private readonly todoAttachmentService: TodoAttachmentService,
  ) {}

  /**
   * Thêm tệp đính kèm cho công việc
   */
  @Post()
  @ApiOperation({ summary: 'Thêm tệp đính kèm cho công việc' })
  @ApiResponse({
    status: 201,
    description: 'Tệp đính kèm đã được thêm thành công',
    schema: ApiResponseDto.getSchema(TodoAttachmentResponseDto),
  })
  async addAttachment(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateTodoAttachmentDto,
  ): Promise<ApiResponseDto<TodoAttachmentResponseDto>> {
    const attachment = await this.todoAttachmentService.addAttachment(user.id, createDto);
    return ApiResponseDto.created(attachment, 'Đã thêm tệp đính kèm thành công');
  }

  /**
   * Lấy danh sách tệp đính kèm
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách tệp đính kèm' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách tệp đính kèm',
    schema: ApiResponseDto.getPaginatedSchema(TodoAttachmentResponseDto),
  })
  async findAll(
    @Query() query: TodoAttachmentQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<TodoAttachmentResponseDto>>> {
    const paginatedAttachments = await this.todoAttachmentService.findAll(query);
    return ApiResponseDto.paginated(paginatedAttachments, 'Lấy danh sách tệp đính kèm thành công');
  }

  /**
   * Lấy danh sách tệp đính kèm của một công việc
   */
  @Get('by-todo/:todoId')
  @ApiOperation({ summary: 'Lấy danh sách tệp đính kèm của một công việc' })
  @ApiParam({ name: 'todoId', description: 'ID công việc', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách tệp đính kèm của công việc',
    schema: ApiResponseDto.getArraySchema(TodoAttachmentResponseDto),
  })
  async findByTodoId(
    @Param('todoId', ParseIntPipe) todoId: number,
  ): Promise<ApiResponseDto<TodoAttachmentResponseDto[]>> {
    const attachments = await this.todoAttachmentService.findByTodoId(todoId);
    return ApiResponseDto.success(attachments, 'Lấy danh sách tệp đính kèm của công việc thành công');
  }

  /**
   * Lấy chi tiết tệp đính kèm
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết tệp đính kèm' })
  @ApiParam({ name: 'id', description: 'ID tệp đính kèm', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Chi tiết tệp đính kèm',
    schema: ApiResponseDto.getSchema(TodoAttachmentResponseDto),
  })
  async findById(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<TodoAttachmentResponseDto>> {
    const attachment = await this.todoAttachmentService.findById(id);
    return ApiResponseDto.success(attachment, 'Lấy chi tiết tệp đính kèm thành công');
  }

  /**
   * Xóa tệp đính kèm
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa tệp đính kèm' })
  @ApiParam({ name: 'id', description: 'ID tệp đính kèm', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Tệp đính kèm đã được xóa thành công',
    schema: ApiResponseDto.getSchema(null),
  })
  async removeAttachment(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<null>> {
    await this.todoAttachmentService.removeAttachment(user.id, id);
    return ApiResponseDto.success(null, 'Đã xóa tệp đính kèm thành công');
  }
}
