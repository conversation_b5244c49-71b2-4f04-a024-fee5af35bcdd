import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { tenantContext } from '../subscribers/tenant-entity.subscriber';
import { RequestWithUser } from './tenant-security.middleware';

/**
 * Middleware để thiết lập tenantId vào AsyncLocalStorage
 * Middleware này sẽ lấy tenantId từ request và lưu vào context
 * để TenantEntitySubscriber c<PERSON> thể sử dụng
 */
@Injectable()
export class TenantContextMiddleware implements NestMiddleware {
  private readonly logger = new Logger(TenantContextMiddleware.name);

  /**
   * Xử lý request và thiết lập tenantId vào context
   * @param request Request từ client
   * @param response Response từ server
   * @param next Hàm next để chuyển tiếp request
   */
  use(request: RequestWithUser, response: Response, next: NextFunction): void {
    // Lấy tenantId từ request (đã đượ<PERSON> thiết lập bởi TenantSecurityMiddleware)
    let tenantId = request.tenantId;

    if (tenantId) {
      // Đảm bảo tenantId là số
      if (typeof tenantId === 'string') {
        tenantId = parseInt(tenantId, 10);
        this.logger.debug(`Chuyển đổi tenantId từ chuỗi sang số: ${tenantId}`);
      }

      // Thiết lập tenantId vào context
      tenantContext.run({ tenantId }, () => {
        this.logger.debug(`Thiết lập tenantId ${tenantId} vào context`);
        next();
      });
    } else {
      // Nếu không có tenantId, tiếp tục xử lý request mà không thiết lập context
      next();
    }
  }
}
