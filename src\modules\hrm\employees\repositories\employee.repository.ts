import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Employee } from '../entities/employee.entity';
import { EmployeeQueryDto } from '../dto/employee-query.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { EmployeeStatus } from '../enum/employee-status.enum';

/**
 * Repository for employee entity
 */
@Injectable()
export class EmployeeRepository {
  private readonly logger = new Logger(EmployeeRepository.name);

  constructor(
    @InjectRepository(Employee)
    private readonly repository: Repository<Employee>,
  ) {}

  /**
   * Find all employees with pagination and filtering
   * @param query Query parameters
   * @returns Paginated list of employees
   */
  async findAll(query: EmployeeQueryDto): Promise<PaginatedResult<Employee>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'employeeCode',
      sortDirection = 'ASC',
      departmentId,
      managerId,
      status,
      employmentType,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('employee');

    // Apply filters if provided
    if (departmentId) {
      queryBuilder.andWhere('employee.departmentId = :departmentId', { departmentId });
    }

    if (managerId) {
      queryBuilder.andWhere('employee.managerId = :managerId', { managerId });
    }

    if (status) {
      queryBuilder.andWhere('employee.status = :status', { status });
    }

    if (employmentType) {
      queryBuilder.andWhere('employee.employmentType = :employmentType', { employmentType });
    }

    // Apply search filter if provided
    if (search) {
      queryBuilder.andWhere('employee.employeeCode ILIKE :search', { search: `%${search}%` });
    }

    // Apply sorting
    queryBuilder.orderBy(`employee.${sortBy}`, sortDirection);

    // Apply pagination
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Find employee by ID
   * @param id Employee ID
   * @returns Employee or null if not found
   */
  async findById(id: number): Promise<Employee | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Find employee by user ID
   * @param userId User ID
   * @returns Employee or null if not found
   */
  async findByUserId(userId: number): Promise<Employee | null> {
    return this.repository.findOne({
      where: { userId },
    });
  }

  /**
   * Find employee by employee code
   * @param employeeCode Employee code
   * @returns Employee or null if not found
   */
  async findByEmployeeCode(employeeCode: string): Promise<Employee | null> {
    return this.repository.findOne({
      where: { employeeCode },
    });
  }

  /**
   * Find employees by department ID
   * @param departmentId Department ID
   * @returns List of employees
   */
  async findByDepartmentId(departmentId: number): Promise<Employee[]> {
    return this.repository.find({
      where: { departmentId },
      order: { employeeCode: 'ASC' },
    });
  }

  /**
   * Find employees by manager ID
   * @param managerId Manager ID
   * @returns List of employees
   */
  async findByManagerId(managerId: number): Promise<Employee[]> {
    return this.repository.find({
      where: { managerId },
      order: { employeeCode: 'ASC' },
    });
  }

  /**
   * Create a new employee
   * @param data Employee data
   * @returns Created employee
   */
  async create(data: Partial<Employee>): Promise<Employee> {
    const employee = this.repository.create(data);
    return this.repository.save(employee);
  }

  /**
   * Update employee
   * @param id Employee ID
   * @param data Updated employee data
   * @returns Updated employee or null if not found
   */
  async update(id: number, data: Partial<Employee>): Promise<Employee | null> {
    await this.repository.update({ id }, data);
    return this.findById(id);
  }

  /**
   * Delete employee
   * @param id Employee ID
   * @returns True if deleted, false if not found
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete({ id });
    return (result.affected ?? 0) > 0;
  }

  /**
   * Update employee status
   * @param id Employee ID
   * @param status New status
   * @returns Updated employee or null if not found
   */
  async updateStatus(id: number, status: EmployeeStatus): Promise<Employee | null> {
    await this.repository.update({ id }, { status });
    return this.findById(id);
  }

  /**
   * Count employees by department
   * @returns List of department IDs with employee counts
   */
  async countByDepartment(): Promise<{ departmentId: number; count: number }[]> {
    const result = await this.repository
      .createQueryBuilder('employee')
      .select('employee.departmentId', 'departmentId')
      .addSelect('COUNT(employee.id)', 'count')
      .where('employee.departmentId IS NOT NULL')
      .groupBy('employee.departmentId')
      .getRawMany();

    return result.map(item => ({
      departmentId: parseInt(item.departmentId),
      count: parseInt(item.count),
    }));
  }
}
