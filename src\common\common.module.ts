import { Module, Global, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { DiscoveryModule, MetadataScanner, Reflector } from '@nestjs/core';
import { GlobalExceptionFilter } from '@filters/global-exception.filter';
import { ErrorHandlingInterceptor } from '@interceptors/error-handling.interceptor';
import { TenantSecurityMiddleware } from './middlewares/tenant-security.middleware';
import { TenantContextMiddleware } from './middlewares/tenant-context.middleware';
import { TenantSecurityProvider } from './providers/tenant-security.provider';
import { TenantEntitySubscriber } from './subscribers/tenant-entity.subscriber';
import { TypeOrmModule } from '@nestjs/typeorm';

/**
 * Module chung cung cấp các thành phần dùng chung cho toàn bộ ứng dụng
 */
@Global()
@Module({
  imports: [DiscoveryModule, TypeOrmModule],
  providers: [
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ErrorHandlingInterceptor,
    },
    TenantSecurityProvider,
    TenantEntitySubscriber,
    MetadataScanner,
    Reflector,
  ],
  exports: [TenantSecurityProvider, TenantEntitySubscriber],
})
export class CommonModule implements NestModule {
  constructor(private readonly tenantSecurityProvider: TenantSecurityProvider) {}

  configure(consumer: MiddlewareConsumer) {
    // Áp dụng middleware cho tất cả các route
    // Lưu ý: TenantSecurityMiddleware phải chạy trước TenantContextMiddleware
    // để đảm bảo tenantId đã được thiết lập trong request
    consumer
      .apply(TenantSecurityMiddleware, TenantContextMiddleware)
      .forRoutes('*');
  }
}
