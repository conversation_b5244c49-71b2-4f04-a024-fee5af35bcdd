import { Test, TestingModule } from '@nestjs/testing';
import { SocketGateway } from '../socket.gateway';
import { SocketService } from '../socket.service';
import { SocketMock, ServerMock, mockUser } from './mocks';
import { SocketEvents } from '../events/socket-events.enum';
import { WsException } from '@nestjs/websockets';
import { Logger } from '@nestjs/common';
import { MessageType } from '../dto/socket-message.dto';

describe('SocketGateway', () => {
  let gateway: SocketGateway;
  let socketService: SocketService;

  beforeEach(async () => {
    // Tạo mock cho Logger
    jest.spyOn(Logger.prototype, 'log').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'error').mockImplementation(() => {});

    // Tạo module test
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SocketGateway,
        {
          provide: SocketService,
          useValue: {
            setServer: jest.fn(),
            addClient: jest.fn(),
            removeClient: jest.fn(),
            addUserToRoom: jest.fn(),
            removeUserFromRoom: jest.fn(),
            sendToRoom: jest.fn(),
            sendToUser: jest.fn(),
            getUsersInRoom: jest.fn().mockReturnValue([]),
          },
        },
      ],
    }).compile();

    // Lấy gateway và service từ module
    gateway = module.get<SocketGateway>(SocketGateway);
    socketService = module.get<SocketService>(SocketService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(gateway).toBeDefined();
  });

  describe('afterInit', () => {
    it('should set server in socket service', () => {
      // Arrange
      const server = new ServerMock();

      // Act
      gateway.afterInit(server as any);

      // Assert
      expect(socketService.setServer).toHaveBeenCalledWith(server);
    });
  });

  describe('handleConnection', () => {
    it('should add client to socket service', () => {
      // Arrange
      const client = new SocketMock();

      // Act
      gateway.handleConnection(client as any);

      // Assert
      expect(socketService.addClient).toHaveBeenCalledWith(client);
    });

    it('should handle error when adding client', () => {
      // Arrange
      const client = new SocketMock();
      const error = new Error('Test error');
      jest.spyOn(socketService, 'addClient').mockImplementation(() => {
        throw error;
      });
      const errorSpy = jest.spyOn(Logger.prototype, 'error');

      // Act
      gateway.handleConnection(client as any);

      // Assert
      expect(errorSpy).toHaveBeenCalled();
    });
  });

  describe('handleDisconnect', () => {
    it('should remove client from socket service', () => {
      // Arrange
      const client = new SocketMock();

      // Act
      gateway.handleDisconnect(client as any);

      // Assert
      expect(socketService.removeClient).toHaveBeenCalledWith(client.id);
    });

    it('should handle error when removing client', () => {
      // Arrange
      const client = new SocketMock();
      const error = new Error('Test error');
      jest.spyOn(socketService, 'removeClient').mockImplementation(() => {
        throw error;
      });
      const errorSpy = jest.spyOn(Logger.prototype, 'error');

      // Act
      gateway.handleDisconnect(client as any);

      // Assert
      expect(errorSpy).toHaveBeenCalled();
    });
  });

  describe('handleAuthenticate', () => {
    it('should return success message', async () => {
      // Arrange
      const client = new SocketMock();
      const payload = { token: 'valid-token' };

      // Act
      const result = await gateway.handleAuthenticate(client as any, payload);

      // Assert
      expect(result).toEqual({
        event: 'authenticated',
        data: {
          status: 'success',
          message: 'Authentication successful',
        },
      });
    });

    it('should throw WsException when authentication fails', async () => {
      // Arrange
      const client = new SocketMock();
      const payload = { token: 'invalid-token' };

      // Mock implementation to throw error
      jest.spyOn(client, 'emit').mockImplementation(() => {
        throw new Error('Authentication failed');
      });

      // Act & Assert
      await expect(gateway.handleAuthenticate(client as any, payload)).rejects.toThrow(WsException);
    });
  });

  describe('handleJoinRoom', () => {
    it('should join room and notify users', async () => {
      // Arrange
      const client = new SocketMock();
      client.setUser(mockUser);
      const joinSpy = jest.spyOn(client, 'join');
      const emitSpy = jest.spyOn(client, 'emit');
      const toSpy = jest.spyOn(client, 'to').mockReturnValue({
        emit: jest.fn(),
      } as any);

      const data = { roomId: 'room-123' };

      // Act
      const result = await gateway.handleJoinRoom(client as any, data, mockUser);

      // Assert
      expect(joinSpy).toHaveBeenCalledWith('room-123');
      expect(socketService.addUserToRoom).toHaveBeenCalledWith('room-123', mockUser.id);
      expect(emitSpy).toHaveBeenCalledWith(SocketEvents.ROOM_JOINED, expect.any(Object));
      expect(toSpy).toHaveBeenCalledWith('room-123');
      expect(result).toEqual({
        event: SocketEvents.ROOM_JOINED,
        data: {
          roomId: 'room-123',
          usersCount: 0,
        },
      });
    });

    it('should throw WsException when joining room fails', async () => {
      // Arrange
      const client = new SocketMock();
      client.setUser(mockUser);
      const error = new Error('Join room failed');
      jest.spyOn(client, 'join').mockImplementation(() => {
        throw error;
      });

      const data = { roomId: 'room-123' };

      // Act & Assert
      await expect(gateway.handleJoinRoom(client as any, data, mockUser)).rejects.toThrow(WsException);
    });
  });

  describe('handleLeaveRoom', () => {
    it('should leave room and notify users', async () => {
      // Arrange
      const client = new SocketMock();
      client.setUser(mockUser);
      const leaveSpy = jest.spyOn(client, 'leave');
      const emitSpy = jest.spyOn(client, 'emit');
      const toSpy = jest.spyOn(client, 'to').mockReturnValue({
        emit: jest.fn(),
      } as any);

      const data = { roomId: 'room-123' };

      // Act
      const result = await gateway.handleLeaveRoom(client as any, data, mockUser);

      // Assert
      expect(leaveSpy).toHaveBeenCalledWith('room-123');
      expect(socketService.removeUserFromRoom).toHaveBeenCalledWith('room-123', mockUser.id);
      expect(emitSpy).toHaveBeenCalledWith(SocketEvents.ROOM_LEFT, expect.any(Object));
      expect(toSpy).toHaveBeenCalledWith('room-123');
      expect(result).toEqual({
        event: SocketEvents.ROOM_LEFT,
        data: {
          roomId: 'room-123',
        },
      });
    });

    it('should throw WsException when leaving room fails', async () => {
      // Arrange
      const client = new SocketMock();
      client.setUser(mockUser);
      const error = new Error('Leave room failed');
      jest.spyOn(client, 'leave').mockImplementation(() => {
        throw error;
      });

      const data = { roomId: 'room-123' };

      // Act & Assert
      await expect(gateway.handleLeaveRoom(client as any, data, mockUser)).rejects.toThrow(WsException);
    });
  });

  describe('handleMessage', () => {
    it('should send message to room', async () => {
      // Arrange
      const client = new SocketMock();
      client.setUser(mockUser);

      const data = {
        content: 'Test message',
        roomId: 'room-123',
        type: MessageType.TEXT,
      };

      jest.spyOn(socketService, 'getUsersInRoom').mockReturnValue([mockUser.id]);

      // Act
      const result = await gateway.handleMessage(client as any, data, mockUser);

      // Assert
      expect(socketService.sendToRoom).toHaveBeenCalledWith(
        'room-123',
        SocketEvents.MESSAGE_RECEIVED,
        expect.any(Object),
        mockUser.id,
      );
      expect(result).toEqual({
        event: SocketEvents.MESSAGE_RECEIVED,
        data: expect.objectContaining({
          status: 'sent',
        }),
      });
    });

    it('should send message to user', async () => {
      // Arrange
      const client = new SocketMock();
      client.setUser(mockUser);

      const data = {
        content: 'Test message',
        recipientId: 'user-456',
        type: MessageType.TEXT,
      };

      // Act
      const result = await gateway.handleMessage(client as any, data, mockUser);

      // Assert
      expect(socketService.sendToUser).toHaveBeenCalledWith(
        'user-456',
        SocketEvents.MESSAGE_RECEIVED,
        expect.any(Object),
      );
      expect(result).toEqual({
        event: SocketEvents.MESSAGE_RECEIVED,
        data: expect.objectContaining({
          status: 'sent',
        }),
      });
    });

    it('should throw WsException when user is not in room', async () => {
      // Arrange
      const client = new SocketMock();
      client.setUser(mockUser);

      const data = {
        content: 'Test message',
        roomId: 'room-123',
        type: MessageType.TEXT,
      };

      jest.spyOn(socketService, 'getUsersInRoom').mockReturnValue(['other-user']);

      // Act & Assert
      await expect(gateway.handleMessage(client as any, data, mockUser)).rejects.toThrow(WsException);
    });

    it('should throw WsException when neither roomId nor recipientId is provided', async () => {
      // Arrange
      const client = new SocketMock();
      client.setUser(mockUser);

      const data = {
        content: 'Test message',
        type: MessageType.TEXT,
      };

      // Act & Assert
      await expect(gateway.handleMessage(client as any, data, mockUser)).rejects.toThrow(WsException);
    });
  });

  describe('handleTyping', () => {
    it('should send typing event to room', () => {
      // Arrange
      const client = new SocketMock();
      client.setUser(mockUser);
      const toSpy = jest.spyOn(client, 'to').mockReturnValue({
        emit: jest.fn(),
      } as any);

      const data = { roomId: 'room-123' };

      // Act
      const result = gateway.handleTyping(client as any, data, mockUser);

      // Assert
      expect(toSpy).toHaveBeenCalledWith('room-123');
      expect(result).toEqual({ status: 'success' });
    });

    it('should send typing event to user', () => {
      // Arrange
      const client = new SocketMock();
      client.setUser(mockUser);

      const data = { recipientId: 'user-456' };

      // Act
      const result = gateway.handleTyping(client as any, data, mockUser);

      // Assert
      expect(socketService.sendToUser).toHaveBeenCalledWith(
        'user-456',
        SocketEvents.TYPING,
        expect.any(Object),
      );
      expect(result).toEqual({ status: 'success' });
    });

    it('should handle error when sending typing event', () => {
      // Arrange
      const client = new SocketMock();
      client.setUser(mockUser);
      const error = new Error('Typing event failed');
      jest.spyOn(client, 'to').mockImplementation(() => {
        throw error;
      });

      const data = { roomId: 'room-123' };

      // Act & Assert
      expect(() => gateway.handleTyping(client as any, data, mockUser)).toThrow(WsException);
    });
  });

  describe('handleStopTyping', () => {
    it('should send stop typing event to room', () => {
      // Arrange
      const client = new SocketMock();
      client.setUser(mockUser);
      const toSpy = jest.spyOn(client, 'to').mockReturnValue({
        emit: jest.fn(),
      } as any);

      const data = { roomId: 'room-123' };

      // Act
      const result = gateway.handleStopTyping(client as any, data, mockUser);

      // Assert
      expect(toSpy).toHaveBeenCalledWith('room-123');
      expect(result).toEqual({ status: 'success' });
    });

    it('should send stop typing event to user', () => {
      // Arrange
      const client = new SocketMock();
      client.setUser(mockUser);

      const data = { recipientId: 'user-456' };

      // Act
      const result = gateway.handleStopTyping(client as any, data, mockUser);

      // Assert
      expect(socketService.sendToUser).toHaveBeenCalledWith(
        'user-456',
        SocketEvents.STOP_TYPING,
        expect.any(Object),
      );
      expect(result).toEqual({ status: 'success' });
    });

    it('should handle error when sending stop typing event', () => {
      // Arrange
      const client = new SocketMock();
      client.setUser(mockUser);
      const error = new Error('Stop typing event failed');
      jest.spyOn(client, 'to').mockImplementation(() => {
        throw error;
      });

      const data = { roomId: 'room-123' };

      // Act & Assert
      expect(() => gateway.handleStopTyping(client as any, data, mockUser)).toThrow(WsException);
    });
  });
});
