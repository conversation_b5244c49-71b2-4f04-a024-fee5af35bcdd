import { SocialProvider } from "../../enum";

/**
 * Interface cho thông tin người dùng từ mạng xã hội
 */
export interface SocialUserInfo {
  id: string;
  email?: string;
  name?: string;
  firstName?: string;
  lastName?: string;
  displayName?: string;
  photoUrl?: string;
  provider: SocialProvider;
  accessToken: string;
  refreshToken?: string;
  tokenExpiresAt?: number;
  rawProfile?: any;
}

/**
 * Interface cho các service xác thực mạng xã hội
 */
export interface ISocialAuthProvider {
  /**
   * Lấy thông tin người dùng từ access token
   * @param accessToken Access token từ nhà cung cấp mạng xã hội
   * @returns Thông tin người dùng
   */
  getUserInfo(accessToken: string): Promise<SocialUserInfo>;

  /**
   * Xác thực access token
   * @param accessToken Access token từ nhà cung cấp mạng xã hội
   * @returns True nếu token hợp lệ, false nếu không
   */
  verifyToken(accessToken: string): Promise<boolean>;

  /**
   * Lấy loại nhà cung cấp mạng xã hội
   * @returns Loại nhà cung cấp mạng xã hội
   */
  getProvider(): SocialProvider;
}
