import { Body, Controller, Delete, Get, Param, Post, Put, UseGuards } from '@nestjs/common';
import { ApiExtraModels, ApiOperation, ApiResponse, ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { AdminTemplateEmailService } from '../services/admin-template-email.service';
import {
  CreateAdminTemplateEmailDto,
  UpdateAdminTemplateEmailDto,
  SendTemplateEmailDto,
  AdminTemplateEmailListDto
} from '../dto/admin-template-email.dto';
import { plainToInstance } from 'class-transformer';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import { AdminTemplateEmail } from '../entities/admin-template-email.entity';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { JwtUserGuard } from '@/modules/auth/guards';
import { CurrentUser } from '@/modules/auth/decorators';
import { RequirePermissionEnum } from '@/modules/auth/decorators/require-module-action.decorator';
import { Permission } from '@/modules/auth/enum/permission.enum';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';

@ApiTags(SWAGGER_API_TAG.SYSTEM)
@ApiExtraModels(ApiResponseDto, AdminTemplateEmail, AdminTemplateEmailListDto)
@Controller('admin/email-templates')
@UseGuards(JwtUserGuard, PermissionsGuard)
@ApiBearerAuth('JWT-auth')
export class AdminTemplateEmailController {
  constructor(private readonly adminTemplateEmailService: AdminTemplateEmailService) {}

  @Get()
  @ApiOperation({ summary: 'Lấy danh sách tất cả template email' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách template email',
    schema: ApiResponseDto.getArraySchema(AdminTemplateEmailListDto),
  })
  @RequirePermissionEnum(Permission.SYSTEM_SETTING_MANAGE_EMAIL)
  async findAll(): Promise<ApiResponseDto<AdminTemplateEmailListDto[]>> {
    const templates = await this.adminTemplateEmailService.findAllWithoutContent();

    // Chuyển đổi sang DTO để chỉ lấy các trường cần thiết
    const templateDtos = templates.map(template =>
      plainToInstance(AdminTemplateEmailListDto, template, { excludeExtraneousValues: true })
    );

    return ApiResponseDto.success(templateDtos);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin template email theo ID' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin template email',
    schema: ApiResponseDto.getSchema(AdminTemplateEmail),
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy template email' })
  @RequirePermissionEnum(Permission.SYSTEM_SETTING_MANAGE_EMAIL)
  async findById(@Param('id') id: number): Promise<ApiResponseDto<AdminTemplateEmail>> {
    const template = await this.adminTemplateEmailService.findById(id);
    return ApiResponseDto.success(template);
  }

  @Get('category/:category')
  @ApiOperation({ summary: 'Lấy thông tin template email theo danh mục' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin template email',
    schema: ApiResponseDto.getSchema(AdminTemplateEmail),
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy template email' })
  @RequirePermissionEnum(Permission.SYSTEM_SETTING_MANAGE_EMAIL)
  async findByCategory(@Param('category') category: string): Promise<ApiResponseDto<AdminTemplateEmail>> {
    const template = await this.adminTemplateEmailService.findByCategory(category);
    return ApiResponseDto.success(template);
  }

  @Post()
  @ApiOperation({ summary: 'Tạo mới template email' })
  @ApiResponse({
    status: 201,
    description: 'Template email đã được tạo',
    schema: ApiResponseDto.getSchema(AdminTemplateEmail),
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @RequirePermissionEnum(Permission.SYSTEM_SETTING_MANAGE_EMAIL)
  async create(
    @Body() createDto: CreateAdminTemplateEmailDto,
    @CurrentUser() user: any,
  ): Promise<ApiResponseDto<AdminTemplateEmail>> {
    // Thêm ID của nhân viên tạo
    createDto.createdBy = user.id;

    const template = await this.adminTemplateEmailService.create(createDto);
    return ApiResponseDto.created(template);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật template email' })
  @ApiResponse({
    status: 200,
    description: 'Template email đã được cập nhật',
    schema: ApiResponseDto.getSchema(AdminTemplateEmail),
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy template email' })
  @RequirePermissionEnum(Permission.SYSTEM_SETTING_MANAGE_EMAIL)
  async update(
    @Param('id') id: number,
    @Body() updateDto: UpdateAdminTemplateEmailDto,
    @CurrentUser() user: any,
  ): Promise<ApiResponseDto<AdminTemplateEmail>> {
    // Thêm ID của nhân viên cập nhật
    updateDto.updatedBy = user.id;

    const template = await this.adminTemplateEmailService.update(id, updateDto);
    return ApiResponseDto.success(template);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Xóa template email' })
  @ApiResponse({
    status: 200,
    description: 'Template email đã được xóa',
    schema: ApiResponseDto.getSchema(Boolean),
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy template email' })
  @RequirePermissionEnum(Permission.SYSTEM_SETTING_MANAGE_EMAIL)
  async delete(@Param('id') id: number): Promise<ApiResponseDto<boolean>> {
    const result = await this.adminTemplateEmailService.delete(id);
    return ApiResponseDto.deleted(result);
  }

  @Post('send')
  @ApiOperation({ summary: 'Gửi email sử dụng template' })
  @ApiResponse({
    status: 200,
    description: 'Email đã được gửi',
    schema: ApiResponseDto.getSchema(Boolean),
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy template email' })
  @RequirePermissionEnum(Permission.SYSTEM_SETTING_MANAGE_EMAIL)
  async sendTemplateEmail(@Body() sendDto: SendTemplateEmailDto): Promise<ApiResponseDto<boolean>> {
    const result = await this.adminTemplateEmailService.sendTemplateEmail(sendDto);
    return ApiResponseDto.success(result);
  }
}
