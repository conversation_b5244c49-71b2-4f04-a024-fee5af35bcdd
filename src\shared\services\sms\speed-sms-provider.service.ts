import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { BaseSmsProvider } from './base-sms-provider.service';
import { SmsResponse, BulkSmsResponse, MessageStatusResponse, ConnectionTestResponse, MessageStatus } from './sms-provider.interface';

/**
 * Interface pour la configuration de SpeedSMS
 */
export interface SpeedSmsConfig {
  /**
   * Token d'accès à l'API SpeedSMS
   */
  apiToken: string;

  /**
   * URL de base de l'API SpeedSMS
   */
  apiUrl?: string;

  /**
   * Type de SMS (2: CSKH, 3: Brandname, 4: Notify, 5: App Android, 6: Numéro fixe)
   */
  smsType?: number;

  /**
   * Nom de l'expéditeur (obligatoire si smsType = 3 ou 5)
   */
  sender?: string;
}

/**
 * Service d'intégration avec l'API SpeedSMS
 */
@Injectable()
export class SpeedSmsProvider extends BaseSmsProvider {
  readonly providerName = 'SpeedSMS';
  
  private readonly apiUrl: string;
  private readonly apiToken: string;
  private readonly defaultSmsType: number;
  private readonly defaultSender: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    super('SpeedSmsProvider');
    
    // Charger la configuration depuis les variables d'environnement ou utiliser des valeurs par défaut
    this.apiToken = this.configService.get<string>('SPEED_SMS_API_TOKEN') || '';
    this.apiUrl = this.configService.get<string>('SPEED_SMS_API_URL') || 'https://api.speedsms.vn/index.php';
    this.defaultSmsType = this.configService.get<number>('SPEED_SMS_TYPE') || 2; // Par défaut: CSKH
    this.defaultSender = this.configService.get<string>('SPEED_SMS_SENDER') || '';
  }

  /**
   * Envoie un SMS à un numéro de téléphone via SpeedSMS
   * @param phoneNumber Numéro de téléphone du destinataire
   * @param message Contenu du message
   * @param options Options supplémentaires (smsType, sender)
   * @returns Promesse contenant l'ID du message ou une erreur
   */
  async sendSms(phoneNumber: string, message: string, options?: any): Promise<SmsResponse> {
    try {
      this.logger.debug(`Envoi d'un SMS à ${phoneNumber} via SpeedSMS`);
      
      const smsType = options?.smsType || this.defaultSmsType;
      const sender = options?.sender || this.defaultSender;
      
      // Vérifier si un sender est requis mais non fourni
      if ((smsType === 3 || smsType === 5) && !sender) {
        throw new Error('Le paramètre "sender" est obligatoire pour les SMS de type Brandname ou Android App');
      }
      
      const formattedPhoneNumber = this.formatPhoneNumber(phoneNumber);
      
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.apiUrl}/sms/send`,
          {
            to: [formattedPhoneNumber],
            content: message,
            sms_type: smsType,
            sender: sender
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Basic ${Buffer.from(this.apiToken + ':x').toString('base64')}`
            }
          }
        ).pipe(
          catchError(error => {
            this.logger.error(`Erreur lors de l'envoi du SMS via SpeedSMS: ${error.message}`, error.stack);
            throw error;
          })
        )
      );
      
      if (response.data.status === 'success') {
        return {
          success: true,
          messageId: response.data.data.tranId.toString(),
          rawResponse: response.data
        };
      } else {
        return {
          success: false,
          errorCode: response.data.code,
          errorMessage: response.data.message || 'Erreur inconnue',
          rawResponse: response.data
        };
      }
    } catch (error) {
      this.logger.error(`Exception lors de l'envoi du SMS via SpeedSMS: ${error.message}`, error.stack);
      return {
        success: false,
        errorMessage: error.message || 'Erreur inconnue'
      };
    }
  }

  /**
   * Envoie un SMS à plusieurs numéros de téléphone via SpeedSMS
   * @param phoneNumbers Liste des numéros de téléphone des destinataires
   * @param message Contenu du message
   * @param options Options supplémentaires (smsType, sender)
   * @returns Promesse contenant les résultats pour chaque destinataire
   */
  async sendBulkSms(phoneNumbers: string[], message: string, options?: any): Promise<BulkSmsResponse> {
    try {
      this.logger.debug(`Envoi de SMS en masse à ${phoneNumbers.length} destinataires via SpeedSMS`);
      
      const smsType = options?.smsType || this.defaultSmsType;
      const sender = options?.sender || this.defaultSender;
      
      // Vérifier si un sender est requis mais non fourni
      if ((smsType === 3 || smsType === 5) && !sender) {
        throw new Error('Le paramètre "sender" est obligatoire pour les SMS de type Brandname ou Android App');
      }
      
      // Formater tous les numéros de téléphone
      const formattedPhoneNumbers = phoneNumbers.map(phone => this.formatPhoneNumber(phone));
      
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.apiUrl}/sms/send`,
          {
            to: formattedPhoneNumbers,
            content: message,
            sms_type: smsType,
            sender: sender
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Basic ${Buffer.from(this.apiToken + ':x').toString('base64')}`
            }
          }
        ).pipe(
          catchError(error => {
            this.logger.error(`Erreur lors de l'envoi des SMS en masse via SpeedSMS: ${error.message}`, error.stack);
            throw error;
          })
        )
      );
      
      if (response.data.status === 'success') {
        // Créer les résultats pour chaque numéro
        const results = formattedPhoneNumbers.map(phone => {
          // Vérifier si le numéro est dans la liste des numéros invalides
          const isInvalid = response.data.data.invalidPhone && response.data.data.invalidPhone.includes(phone);
          
          if (isInvalid) {
            return {
              phoneNumber: phone,
              success: false,
              errorMessage: 'Numéro de téléphone invalide'
            };
          } else {
            return {
              phoneNumber: phone,
              success: true,
              messageId: response.data.data.tranId.toString()
            };
          }
        });
        
        // Compter les succès et les échecs
        const successCount = results.filter(r => r.success).length;
        const failureCount = results.filter(r => !r.success).length;
        
        return {
          successCount,
          failureCount,
          results,
          transactionId: response.data.data.tranId.toString(),
          totalCost: response.data.data.totalPrice,
          rawResponse: response.data
        };
      } else {
        // En cas d'erreur globale, tous les messages sont considérés comme échoués
        const results = formattedPhoneNumbers.map(phone => ({
          phoneNumber: phone,
          success: false,
          errorCode: response.data.code,
          errorMessage: response.data.message || 'Erreur inconnue'
        }));
        
        return {
          successCount: 0,
          failureCount: formattedPhoneNumbers.length,
          results,
          rawResponse: response.data
        };
      }
    } catch (error) {
      this.logger.error(`Exception lors de l'envoi des SMS en masse via SpeedSMS: ${error.message}`, error.stack);
      
      // En cas d'exception, tous les messages sont considérés comme échoués
      const results = phoneNumbers.map(phone => ({
        phoneNumber: phone,
        success: false,
        errorMessage: error.message || 'Erreur inconnue'
      }));
      
      return {
        successCount: 0,
        failureCount: phoneNumbers.length,
        results
      };
    }
  }

  /**
   * Vérifie le statut d'un message envoyé via SpeedSMS
   * @param messageId ID du message à vérifier
   * @returns Promesse contenant le statut du message
   */
  async checkMessageStatus(messageId: string): Promise<MessageStatusResponse> {
    try {
      this.logger.debug(`Vérification du statut du message ${messageId} via SpeedSMS`);
      
      const response = await firstValueFrom(
        this.httpService.get(
          `${this.apiUrl}/sms/status/${messageId}`,
          {
            headers: {
              'Authorization': `Basic ${Buffer.from(this.apiToken + ':x').toString('base64')}`
            }
          }
        ).pipe(
          catchError(error => {
            this.logger.error(`Erreur lors de la vérification du statut du message via SpeedSMS: ${error.message}`, error.stack);
            throw error;
          })
        )
      );
      
      if (response.data.status === 'success') {
        // Créer un objet pour stocker les statuts par numéro
        const statusByPhone = {};
        
        // Parcourir les résultats et stocker les statuts
        for (const result of response.data.data) {
          statusByPhone[result.phone] = this.mapSpeedSmsStatus(result.status);
        }
        
        // Déterminer le statut global
        let globalStatus = MessageStatus.UNKNOWN;
        const statuses = Object.values(statusByPhone);
        
        if (statuses.length === 0) {
          globalStatus = MessageStatus.UNKNOWN;
        } else if (statuses.every(s => s === MessageStatus.DELIVERED)) {
          globalStatus = MessageStatus.DELIVERED;
        } else if (statuses.every(s => s === MessageStatus.FAILED)) {
          globalStatus = MessageStatus.FAILED;
        } else if (statuses.some(s => s === MessageStatus.SENDING)) {
          globalStatus = MessageStatus.SENDING;
        } else if (statuses.some(s => s === MessageStatus.PENDING)) {
          globalStatus = MessageStatus.PENDING;
        } else {
          // Statut mixte
          globalStatus = MessageStatus.SENDING;
        }
        
        return {
          messageId,
          status: globalStatus,
          updatedAt: new Date(),
          details: JSON.stringify(statusByPhone),
          rawResponse: response.data
        };
      } else {
        return {
          messageId,
          status: MessageStatus.UNKNOWN,
          updatedAt: new Date(),
          details: response.data.message || 'Erreur inconnue',
          rawResponse: response.data
        };
      }
    } catch (error) {
      this.logger.error(`Exception lors de la vérification du statut du message via SpeedSMS: ${error.message}`, error.stack);
      return {
        messageId,
        status: MessageStatus.UNKNOWN,
        updatedAt: new Date(),
        details: error.message || 'Erreur inconnue'
      };
    }
  }

  /**
   * Envoie un SMS avec un brandname via SpeedSMS
   * @param phoneNumber Numéro de téléphone du destinataire
   * @param message Contenu du message
   * @param brandname Nom de la marque à utiliser comme expéditeur
   * @param options Options supplémentaires
   * @returns Promesse contenant l'ID du message ou une erreur
   */
  async sendBrandnameSms(phoneNumber: string, message: string, brandname: string, options?: any): Promise<SmsResponse> {
    // Pour SpeedSMS, utiliser le type 3 pour les SMS brandname
    return this.sendSms(phoneNumber, message, { 
      ...options, 
      smsType: 3, 
      sender: brandname 
    });
  }

  /**
   * Teste la connexion avec SpeedSMS
   * @param config Configuration de SpeedSMS
   * @returns Promesse indiquant si la connexion est réussie
   */
  async testConnection(config: SpeedSmsConfig): Promise<ConnectionTestResponse> {
    try {
      this.logger.debug('Test de connexion avec SpeedSMS');
      
      const apiToken = config.apiToken || this.apiToken;
      const apiUrl = config.apiUrl || this.apiUrl;
      
      if (!apiToken) {
        return {
          success: false,
          message: 'Token API manquant'
        };
      }
      
      const response = await firstValueFrom(
        this.httpService.get(
          `${apiUrl}/user/info`,
          {
            headers: {
              'Authorization': `Basic ${Buffer.from(apiToken + ':x').toString('base64')}`
            }
          }
        ).pipe(
          catchError(error => {
            this.logger.error(`Erreur lors du test de connexion avec SpeedSMS: ${error.message}`, error.stack);
            throw error;
          })
        )
      );
      
      if (response.data.status === 'success') {
        return {
          success: true,
          message: 'Connexion réussie',
          details: {
            email: response.data.data.email,
            balance: response.data.data.balance,
            currency: response.data.data.currency
          }
        };
      } else {
        return {
          success: false,
          message: response.data.message || 'Erreur inconnue',
          details: response.data
        };
      }
    } catch (error) {
      this.logger.error(`Exception lors du test de connexion avec SpeedSMS: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Erreur inconnue'
      };
    }
  }

  /**
   * Convertit un statut SpeedSMS en statut standard
   * @param speedSmsStatus Statut SpeedSMS
   * @returns Statut standard
   */
  private mapSpeedSmsStatus(speedSmsStatus: number): MessageStatus {
    switch (speedSmsStatus) {
      case 0:
        return MessageStatus.PENDING; // En attente
      case -1:
        return MessageStatus.SENDING; // En cours d'envoi
      case 1:
        return MessageStatus.DELIVERED; // Livré
      case 2:
        return MessageStatus.FAILED; // Échec
      default:
        return MessageStatus.UNKNOWN; // Inconnu
    }
  }
}
