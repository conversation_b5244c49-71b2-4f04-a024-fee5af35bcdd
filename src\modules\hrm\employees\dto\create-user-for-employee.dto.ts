import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsInt,
  IsOptional,
  IsEmail,
  <PERSON><PERSON><PERSON>th,
  <PERSON>Length,
  Matches
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc tạo tài khoản người dùng cho nhân viên
 */
export class CreateUserForEmployeeDto {
  /**
   * ID của nhân viên (tùy chọn)
   * @example 1
   */
  @ApiProperty({ required: false, description: 'ID của nhân viên', example: 1 })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  employeeId?: number;

  /**
   * Tên đăng nhập
   * @example "user001"
   */
  @ApiProperty({ description: 'Tên đăng nhập', example: 'user001' })
  @IsNotEmpty()
  @IsString()
  @MinLength(4)
  @MaxLength(50)
  username: string;

  /**
   * <PERSON><PERSON><PERSON> khẩ<PERSON>
   * @example "Password@123"
   */
  @ApiProperty({ description: 'M<PERSON>t khẩu', example: 'Password@123' })
  @IsNotEmpty()
  @IsString()
  @MinLength(8)
  @MaxLength(50)
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
    {
      message: 'Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt',
    },
  )
  password: string;

  /**
   * Địa chỉ email
   * @example "<EMAIL>"
   */
  @ApiProperty({ description: 'Địa chỉ email', example: '<EMAIL>' })
  @IsNotEmpty()
  @IsEmail()
  @MaxLength(255)
  email: string;

  /**
   * Họ và tên đầy đủ
   * @example "Nguyễn Văn A"
   */
  @ApiProperty({ description: 'Họ và tên đầy đủ', example: 'Nguyễn Văn A' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  fullName: string;

  /**
   * ID phòng ban của người dùng
   * @example 1
   */
  @ApiProperty({ required: false, description: 'ID phòng ban của người dùng', example: 1 })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  departmentId?: number;
}
