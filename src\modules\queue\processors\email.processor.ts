import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { QueueName } from '../queue.constants';
import { SendEmailDto } from '../dto/send-email.dto';

/**
 * Processor xử lý các job email
 */
@Processor(QueueName.EMAIL)
export class EmailProcessor {
  private readonly logger = new Logger(EmailProcessor.name);

  /**
   * Xử lý job gửi email
   * @param job Job cần xử lý
   */
  @Process()
  async handleSendEmail(job: Job<SendEmailDto & { from: string; createdBy: number; createdAt: number }>) {
    try {
      const { to, cc, bcc, subject, content, html, from, createdBy, createdAt } = job.data;

      this.logger.log(`Processing email job ${job.id} to ${to}`);

      // TODO: Implement email sending logic here
      // Ví dụ: Sử dụng nodemailer hoặc các service gửi email khác
      this.logger.log('Email data:', {
        from,
        to,
        cc,
        bcc,
        subject,
        content,
        html,
        createdBy,
        createdAt,
      });

      // Gi<PERSON> lập việc gửi email thành công
      await new Promise((resolve) => setTimeout(resolve, 1000));

      this.logger.log(`Email job ${job.id} completed successfully`);
    } catch (error) {
      this.logger.error(
        `Failed to process email job ${job.id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
} 