import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Entity representing file attachments for todos
 */
@Entity('todo_attachments')
export class TodoAttachment {
  /**
   * Unique identifier for the attachment
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID of the todo the attachment belongs to
   */
  @Column({ name: 'todo_id', type: 'integer', nullable: false })
  todoId: number;

  /**
   * Attachment filename
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  filename: string;

  /**
   * URL to the attachment (can be S3 URL or other path)
   */
  @Column({ type: 'varchar', length: 500, nullable: false })
  url: string;

  /**
   * Content type of the file (MIME type)
   */
  @Column({ name: 'content_type', type: 'varchar', length: 100, nullable: true })
  contentType: string | null;

  /**
   * File size in bytes
   */
  @Column({ type: 'bigint', nullable: true })
  size: number | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * ID of the user who created the attachment
   */
  @Column({ name: 'created_by', type: 'integer', nullable: true })
  createdBy: number | null;

  /**
   * ID of the company/organization that owns this record
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
