import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { Logger } from '@nestjs/common';
import { ConfigModule, ConfigService, ConfigType, DatabaseConfig } from '@/config';
import { Todo } from '../entities/todo.entity';
import { Project } from '../entities/project.entity';
import { ProjectMember } from '../entities/project-members.entity';
import { TodoTag } from '../entities/todo-tag.entity';
import { TaskKr } from '../entities/task-kr.entity';

/**
 * Test kết nối database với các entity của module Todolists
 */
describe('Todolists Database Connection Test', () => {
  let module: TestingModule;
  let dataSource: DataSource;
  const logger = new Logger('TodolistsDatabaseTest');

  beforeAll(async () => {
    // Tạo testing module với TypeOrmModule
    module = await Test.createTestingModule({
      imports: [
        // Import ConfigModule để lấy cấu hình database
        ConfigModule,
        // Import TypeOrmModule với cấu hình từ ConfigService
        TypeOrmModule.forRootAsync({
          inject: [ConfigService],
          useFactory: (configService: ConfigService) => {
            const dbConfig = configService.getConfig<DatabaseConfig>(ConfigType.Database);
            logger.log(`Connecting to database: ${dbConfig.database} on ${dbConfig.host}:${dbConfig.port}`);
            
            return {
              type: 'postgres',
              host: dbConfig.host,
              port: dbConfig.port,
              username: dbConfig.username,
              password: dbConfig.password,
              database: dbConfig.database,
              entities: [Todo, Project, ProjectMember, TodoTag, TaskKr],
              synchronize: false,
              ssl: {
                rejectUnauthorized: !dbConfig.ssl,
              },
            };
          },
        }),
      ],
    }).compile();

    // Lấy DataSource
    dataSource = module.get<DataSource>(DataSource);
  });

  afterAll(async () => {
    // Đóng kết nối sau khi test hoàn thành
    if (dataSource && dataSource.isInitialized) {
      await dataSource.destroy();
    }
    await module.close();
  });

  it('should connect to database successfully', async () => {
    // Kiểm tra DataSource đã được khởi tạo
    expect(dataSource).toBeDefined();
    expect(dataSource.isInitialized).toBe(true);
    
    // Kiểm tra kết nối bằng cách thực hiện một truy vấn đơn giản
    const result = await dataSource.query('SELECT 1 as value');
    expect(result).toBeDefined();
    expect(result[0].value).toBe(1);
    
    logger.log('Database connection test passed successfully');
  });

  it('should have Todo entity registered', () => {
    // Kiểm tra entity Todo đã được đăng ký
    const todoMetadata = dataSource.getMetadata(Todo);
    expect(todoMetadata).toBeDefined();
    expect(todoMetadata.name).toBe('Todo');
    
    // Kiểm tra các trường của entity Todo
    expect(todoMetadata.columns.some(column => column.propertyName === 'id')).toBe(true);
    expect(todoMetadata.columns.some(column => column.propertyName === 'title')).toBe(true);
    expect(todoMetadata.columns.some(column => column.propertyName === 'assigneeId')).toBe(true);
    
    logger.log('Todo entity test passed successfully');
  });

  it('should have Project entity registered', () => {
    // Kiểm tra entity Project đã được đăng ký
    const projectMetadata = dataSource.getMetadata(Project);
    expect(projectMetadata).toBeDefined();
    expect(projectMetadata.name).toBe('Project');
    
    // Kiểm tra các trường của entity Project
    expect(projectMetadata.columns.some(column => column.propertyName === 'id')).toBe(true);
    expect(projectMetadata.columns.some(column => column.propertyName === 'title')).toBe(true);
    expect(projectMetadata.columns.some(column => column.propertyName === 'ownerId')).toBe(true);
    
    logger.log('Project entity test passed successfully');
  });

  it('should have TodoTag entity registered', () => {
    // Kiểm tra entity TodoTag đã được đăng ký
    const todoTagMetadata = dataSource.getMetadata(TodoTag);
    expect(todoTagMetadata).toBeDefined();
    expect(todoTagMetadata.name).toBe('TodoTag');
    
    // Kiểm tra các trường của entity TodoTag
    expect(todoTagMetadata.columns.some(column => column.propertyName === 'id')).toBe(true);
    expect(todoTagMetadata.columns.some(column => column.propertyName === 'todoId')).toBe(true);
    expect(todoTagMetadata.columns.some(column => column.propertyName === 'labelsId')).toBe(true);
    
    logger.log('TodoTag entity test passed successfully');
  });

  it('should have TaskKr entity registered', () => {
    // Kiểm tra entity TaskKr đã được đăng ký
    const taskKrMetadata = dataSource.getMetadata(TaskKr);
    expect(taskKrMetadata).toBeDefined();
    expect(taskKrMetadata.name).toBe('TaskKr');
    
    // Kiểm tra các trường của entity TaskKr
    expect(taskKrMetadata.columns.some(column => column.propertyName === 'taskId')).toBe(true);
    expect(taskKrMetadata.columns.some(column => column.propertyName === 'krId')).toBe(true);
    
    logger.log('TaskKr entity test passed successfully');
  });
});
