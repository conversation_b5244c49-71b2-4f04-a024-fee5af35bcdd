import { Injectable, Logger } from '@nestjs/common';
import { ContractRepository } from '../repositories/contract.repository';
import { Contract } from '../entities/contract.entity';
import { CreateContractDto } from '../dto/create-contract.dto';
import { UpdateContractDto } from '../dto/update-contract.dto';
import { ContractQueryDto } from '../dto/contract-query.dto';
import { UpdateContractStatusDto } from '../dto/update-contract-status.dto';
import { TerminateContractDto } from '../dto/terminate-contract.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common/exceptions/app.exception';
import { HRM_ERROR_CODES } from '../../errors/hrm-error.code';
import { ContractStatus } from '../enum/contract-status.enum';

/**
 * Service for contract management
 */
@Injectable()
export class ContractService {
  private readonly logger = new Logger(ContractService.name);

  constructor(
    private readonly contractRepository: ContractRepository,
  ) {}

  /**
   * Find all contracts with pagination and filtering
   * @param query Query parameters
   * @returns Paginated list of contracts
   */
  async findAll(query: ContractQueryDto): Promise<PaginatedResult<Contract>> {
    try {
      return await this.contractRepository.findAll(query);
    } catch (error) {
      this.logger.error(`Error finding contracts: ${error.message}`, error.stack);
      throw new AppException(
        HRM_ERROR_CODES.CONTRACT_FIND_FAILED,
        `Failed to find contracts: ${error.message}`,
      );
    }
  }

  /**
   * Find contract by ID
   * @param id Contract ID
   * @returns Contract
   * @throws AppException if contract not found
   */
  async findById(id: number): Promise<Contract> {
    try {
      const contract = await this.contractRepository.findById(id);
      if (!contract) {
        throw new AppException(
          HRM_ERROR_CODES.CONTRACT_NOT_FOUND,
          `Contract with ID ${id} not found`,
        );
      }
      return contract;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error finding contract by ID: ${error.message}`, error.stack);
      throw new AppException(
        HRM_ERROR_CODES.CONTRACT_FIND_FAILED,
        `Failed to find contract: ${error.message}`,
      );
    }
  }

  /**
   * Find contracts by employee ID
   * @param employeeId Employee ID
   * @returns List of contracts
   */
  async findByEmployeeId(employeeId: number): Promise<Contract[]> {
    try {
      return await this.contractRepository.findByEmployeeId(employeeId);
    } catch (error) {
      this.logger.error(`Error finding contracts by employee ID: ${error.message}`, error.stack);
      throw new AppException(
        HRM_ERROR_CODES.CONTRACT_FIND_FAILED,
        `Failed to find contracts for employee: ${error.message}`,
      );
    }
  }

  /**
   * Find active contract by employee ID
   * @param employeeId Employee ID
   * @returns Active contract or null if not found
   */
  async findActiveContractByEmployeeId(employeeId: number): Promise<Contract | null> {
    try {
      return await this.contractRepository.findActiveContractByEmployeeId(employeeId);
    } catch (error) {
      this.logger.error(`Error finding active contract by employee ID: ${error.message}`, error.stack);
      throw new AppException(
        HRM_ERROR_CODES.CONTRACT_FIND_FAILED,
        `Failed to find active contract for employee: ${error.message}`,
      );
    }
  }

  /**
   * Find contracts expiring soon
   * @param daysThreshold Number of days to consider "soon"
   * @returns List of contracts expiring soon
   */
  async findContractsExpiringSoon(daysThreshold: number): Promise<Contract[]> {
    try {
      return await this.contractRepository.findContractsExpiringSoon(daysThreshold);
    } catch (error) {
      this.logger.error(`Error finding contracts expiring soon: ${error.message}`, error.stack);
      throw new AppException(
        HRM_ERROR_CODES.CONTRACT_FIND_FAILED,
        `Failed to find contracts expiring soon: ${error.message}`,
      );
    }
  }

  /**
   * Create a new contract
   * @param createContractDto Contract data
   * @param userId ID of the user creating the contract
   * @returns Created contract
   * @throws AppException if contract code already exists
   */
  async create(createContractDto: CreateContractDto, userId: number): Promise<Contract> {
    try {
      // Check if contract code already exists
      const existingContract = await this.contractRepository.findByContractCode(createContractDto.contractCode);
      if (existingContract) {
        throw new AppException(
          HRM_ERROR_CODES.CONTRACT_CODE_EXISTS,
          `Contract with code ${createContractDto.contractCode} already exists`,
        );
      }

      // Create contract
      const now = Date.now();
      const contract = await this.contractRepository.create({
        ...createContractDto,
        status: createContractDto.status || ContractStatus.DRAFT,
        currency: createContractDto.currency || 'VND',
        createdAt: now,
        updatedAt: now,
        createdBy: userId,
        updatedBy: userId,
      });

      return contract;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error creating contract: ${error.message}`, error.stack);
      throw new AppException(
        HRM_ERROR_CODES.CONTRACT_CREATE_FAILED,
        `Failed to create contract: ${error.message}`,
      );
    }
  }

  /**
   * Update contract
   * @param id Contract ID
   * @param updateContractDto Updated contract data
   * @param userId ID of the user updating the contract
   * @returns Updated contract
   * @throws AppException if contract not found
   */
  async update(id: number, updateContractDto: UpdateContractDto, userId: number): Promise<Contract> {
    try {
      // Check if contract exists
      const contract = await this.findById(id);

      // Check if contract can be updated
      if (contract.status === ContractStatus.TERMINATED) {
        throw new AppException(
          HRM_ERROR_CODES.CONTRACT_CANNOT_UPDATE_TERMINATED,
          'Cannot update a terminated contract',
        );
      }

      // Update contract
      const now = Date.now();
      const updatedContract = await this.contractRepository.update(id, {
        ...updateContractDto,
        updatedAt: now,
        updatedBy: userId,
      });

      if (!updatedContract) {
        throw new AppException(
          HRM_ERROR_CODES.CONTRACT_UPDATE_FAILED,
          `Failed to update contract with ID ${id}`,
        );
      }

      return updatedContract;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error updating contract: ${error.message}`, error.stack);
      throw new AppException(
        HRM_ERROR_CODES.CONTRACT_UPDATE_FAILED,
        `Failed to update contract: ${error.message}`,
      );
    }
  }

  /**
   * Update contract status
   * @param id Contract ID
   * @param updateStatusDto Status update data
   * @param userId ID of the user updating the status
   * @returns Updated contract
   * @throws AppException if contract not found
   */
  async updateStatus(id: number, updateStatusDto: UpdateContractStatusDto, userId: number): Promise<Contract> {
    try {
      // Check if contract exists
      const contract = await this.findById(id);

      // Check if status change is valid
      if (contract.status === ContractStatus.TERMINATED && updateStatusDto.status !== ContractStatus.TERMINATED) {
        throw new AppException(
          HRM_ERROR_CODES.CONTRACT_INVALID_STATUS_CHANGE,
          'Cannot change status of a terminated contract',
        );
      }

      // Update status
      const now = Date.now();
      const updatedContract = await this.contractRepository.update(id, {
        status: updateStatusDto.status,
        notes: contract.notes 
          ? `${contract.notes}\n${new Date().toISOString()}: Status changed to ${updateStatusDto.status}${updateStatusDto.reason ? ` - Reason: ${updateStatusDto.reason}` : ''}`
          : `${new Date().toISOString()}: Status changed to ${updateStatusDto.status}${updateStatusDto.reason ? ` - Reason: ${updateStatusDto.reason}` : ''}`,
        updatedAt: now,
        updatedBy: userId,
      });

      if (!updatedContract) {
        throw new AppException(
          HRM_ERROR_CODES.CONTRACT_UPDATE_FAILED,
          `Failed to update contract status with ID ${id}`,
        );
      }

      return updatedContract;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error updating contract status: ${error.message}`, error.stack);
      throw new AppException(
        HRM_ERROR_CODES.CONTRACT_UPDATE_FAILED,
        `Failed to update contract status: ${error.message}`,
      );
    }
  }

  /**
   * Terminate contract
   * @param id Contract ID
   * @param terminateContractDto Termination data
   * @param userId ID of the user terminating the contract
   * @returns Terminated contract
   * @throws AppException if contract not found
   */
  async terminateContract(id: number, terminateContractDto: TerminateContractDto, userId: number): Promise<Contract> {
    try {
      // Check if contract exists
      const contract = await this.findById(id);

      // Check if contract is already terminated
      if (contract.status === ContractStatus.TERMINATED) {
        throw new AppException(
          HRM_ERROR_CODES.CONTRACT_ALREADY_TERMINATED,
          'Contract is already terminated',
        );
      }

      // Terminate contract
      const now = Date.now();
      const terminatedContract = await this.contractRepository.update(id, {
        status: ContractStatus.TERMINATED,
        terminationDate: terminateContractDto.terminationDate,
        terminationReason: terminateContractDto.terminationReason,
        updatedAt: now,
        updatedBy: userId,
      });

      if (!terminatedContract) {
        throw new AppException(
          HRM_ERROR_CODES.CONTRACT_UPDATE_FAILED,
          `Failed to terminate contract with ID ${id}`,
        );
      }

      return terminatedContract;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error terminating contract: ${error.message}`, error.stack);
      throw new AppException(
        HRM_ERROR_CODES.CONTRACT_UPDATE_FAILED,
        `Failed to terminate contract: ${error.message}`,
      );
    }
  }

  /**
   * Delete contract
   * @param id Contract ID
   * @returns True if deleted successfully
   * @throws AppException if contract not found or delete fails
   */
  async delete(id: number): Promise<boolean> {
    try {
      // Check if contract exists
      await this.findById(id);

      // Delete contract
      const deleted = await this.contractRepository.delete(id);
      if (!deleted) {
        throw new AppException(
          HRM_ERROR_CODES.CONTRACT_DELETE_FAILED,
          `Failed to delete contract with ID ${id}`,
        );
      }

      return true;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error deleting contract: ${error.message}`, error.stack);
      throw new AppException(
        HRM_ERROR_CODES.CONTRACT_DELETE_FAILED,
        `Failed to delete contract: ${error.message}`,
      );
    }
  }
}
