import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { SystemEventData } from '../interfaces/system-event-data.type';
import { CommentType } from '../enum/comment-type.enum';

/**
 * Entity đại diện cho bình luận trên todos, bao gồm bình luận người dùng và sự kiện hệ thống
 */
@Entity('todo_comments')
export class TodoComment {
  /**
   * Định danh duy nhất cho bình luận
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID của todo mà bình luận này liên quan
   */
  @Column({ name: 'todo_id', type: 'integer', nullable: true })
  todoId: number | null;

  /**
   * ID của người dùng tạo bình luận hoặc thực hiện sự kiện hệ thống
   */
  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number | null;

  /**
   * Nội dung định dạng HTML, chứa thẻ <img> hoặc <a> để nhúng ảnh/file
   */
  @Column({ name: 'content_html', type: 'text', nullable: false })
  contentHtml: string;

  /**
   * Thời gian tạo bình luận (dạng mili-giây)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * ID của bình luận cha (nếu đây là trả lời)
   */
  @Column({ name: 'parent_id', type: 'integer', nullable: true })
  parentId: number | null;

  /**
   * Loại bình luận (note, question, suggestion, issue), chỉ áp dụng cho bình luận người dùng
   */
  @Column({
    name: 'comment_type',
    type: 'varchar',
    length: 50,
    default: CommentType.NOTE,
    nullable: true,
  })
  commentType: CommentType | null;

  /**
   * ID của công ty/tổ chức sở hữu bình luận này
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;

  /**
   * Xác định đây là bình luận người dùng (false) hay sự kiện hệ thống (true)
   */
  @Column({ name: 'is_system_event', type: 'boolean', default: false })
  isSystemEvent: boolean;

  /**
   * Dữ liệu chi tiết của sự kiện hệ thống (JSON), chỉ áp dụng khi is_system_event = true
   * Lưu trữ các sự kiện như thay đổi trạng thái, cộng tác viên, hoặc người đảm nhiệm, bao gồm thời gian thay đổi
   */
  @Column({ name: 'event_data', type: 'jsonb', nullable: true })
  eventData: SystemEventData | null;

  /**
   * Danh sách tài nguyên (ảnh, file) liên quan, lưu dưới dạng JSON
   * Ví dụ: [{ type: 'image', url: 'https://example.com/photo.jpg', name: 'photo.jpg' }]
   */
  @Column({ name: 'resources', type: 'jsonb', nullable: true })
  resources: Array<{ type: string; url: string; name?: string }> | null;

  /**
   * Danh sách người dùng được mention trong bình luận, lưu dưới dạng JSON
   * Ví dụ: [{ userId: 123, username: 'userA' }, { userId: 456, username: 'userB' }]
   */
  @Column({ name: 'mentions', type: 'jsonb', nullable: true })
  mentions: Array<{ userId: number; username: string }> | null;
}
