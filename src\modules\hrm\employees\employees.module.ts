import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Employee } from './entities/employee.entity';
import { EmployeeRepository } from './repositories/employee.repository';
import { EmployeeService } from './services/employee.service';
import { EmployeeController } from './controllers/employee.controller';
import { EmployeeUserService } from './services/employee-user.service';
import { EmployeeUserController } from './controllers/employee-user.controller';
import { EmployeePermissionService } from './services/employee-permission.service';
import { EmployeePermissionController } from './controllers/employee-permission.controller';
import { EmployeePermissionRepository } from './repositories/employee-permission.repository';
import { RoleService } from './services/role.service';
import { RoleController } from './controllers/role.controller';
import { EmployeeRoleController } from './controllers/employee-role.controller';
import { RoleRepository } from './repositories/role.repository';
import { User } from '@/modules/auth/entities/user.entity';
import { Role } from '@/modules/auth/entities/role.entity';
import { UserRole } from '@/modules/auth/entities/user-role.entity';
import { UserPermission } from '@/modules/auth/entities/user-permission.entity';
import { RolePermission } from '@/modules/auth/entities/role-permission.entity';
import { Permission } from '@/modules/auth/entities/permission.entity';
import { UserRepository } from '@/modules/auth/repositories/user.repository';
import { EncryptionService } from '@/shared/services/encryption.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Employee,
      User,
      Role,
      UserRole,
      UserPermission,
      RolePermission,
      Permission
    ]),
  ],
  controllers: [
    EmployeeController,
    EmployeeUserController,
    EmployeePermissionController,
    EmployeeRoleController,
    RoleController
  ],
  providers: [
    EmployeeService,
    EmployeeRepository,
    EmployeeUserService,
    EmployeePermissionService,
    EmployeePermissionRepository,
    RoleService,
    RoleRepository,
    UserRepository,
    EncryptionService
  ],
  exports: [
    EmployeeService,
    EmployeeRepository,
    EmployeeUserService,
    EmployeePermissionService,
    RoleService,
    RoleRepository
  ],
})
export class EmployeesModule {}
