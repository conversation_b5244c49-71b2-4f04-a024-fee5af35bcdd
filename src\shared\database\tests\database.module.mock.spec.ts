import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '../../../config';
import { DatabaseModule } from '../database.module';
import { DataSource } from 'typeorm';

// Mock DataSource
class MockDataSource {
  options: any = {
    type: 'postgres',
    host: 'localhost',
    port: 5432,
    username: 'test_user',
    password: 'test_password',
    database: 'test_db',
    autoLoadEntities: true,
    synchronize: false,
    ssl: {
      rejectUnauthorized: false,
    },
  };
  isInitialized = true;
  entityMetadatas = [
    {
      name: 'TestEntity',
      columns: [
        { propertyName: 'id', type: 'number' },
        { propertyName: 'name', type: 'string' },
      ],
    },
  ];
  
  async destroy() {
    return Promise.resolve();
  }
  
  async query(sql: string) {
    if (sql === 'SELECT 1 as value') {
      return [{ value: 1 }];
    }
    return [];
  }
}

// Mock ConfigService
class MockConfigService {
  getConfig(configType: string) {
    return {
      host: 'localhost',
      port: 5432,
      username: 'test_user',
      password: 'test_password',
      database: 'test_db',
      ssl: false,
    };
  }
}

describe('DatabaseModule (Mock)', () => {
  let module: TestingModule;
  let dataSource: MockDataSource;
  let configService: MockConfigService;

  beforeEach(async () => {
    // Create a testing module with mocks
    module = await Test.createTestingModule({
      imports: [DatabaseModule],
    })
      .overrideProvider(DataSource)
      .useClass(MockDataSource)
      .overrideProvider(ConfigService)
      .useClass(MockConfigService)
      .compile();

    // Get the mocked instances
    dataSource = module.get<MockDataSource>(DataSource);
    configService = module.get<MockConfigService>(ConfigService);
  });

  afterEach(async () => {
    await module.close();
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
    expect(dataSource).toBeDefined();
    expect(configService).toBeDefined();
  });

  it('should have the correct database configuration', () => {
    // Verify that the DataSource was configured correctly
    expect(dataSource.options).toMatchObject({
      type: 'postgres',
      host: 'localhost',
      port: 5432,
      username: 'test_user',
      password: 'test_password',
      database: 'test_db',
      ssl: {
        rejectUnauthorized: false,
      },
    });
  });

  it('should have autoLoadEntities set to true', () => {
    expect(dataSource.options.autoLoadEntities).toBe(true);
  });

  it('should have synchronize set to false', () => {
    expect(dataSource.options.synchronize).toBe(false);
  });

  it('should be able to execute a simple query', async () => {
    // Try to execute a simple query
    const result = await dataSource.query('SELECT 1 as value');
    expect(result).toBeDefined();
    expect(result[0].value).toBe(1);
  });

  it('should have entities configured', () => {
    // Check if entities are loaded
    const entities = dataSource.entityMetadatas;
    expect(entities.length).toBeGreaterThan(0);
    
    // Check the first entity
    const firstEntity = entities[0];
    expect(firstEntity.name).toBe('TestEntity');
    expect(firstEntity.columns.length).toBe(2);
  });
});
