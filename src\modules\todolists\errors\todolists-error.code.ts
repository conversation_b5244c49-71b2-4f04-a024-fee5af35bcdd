import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@/common/exceptions/error-code';

/**
 * Mã lỗi cho module Todolists
 * Phạm vi mã lỗi: 13000-13099
 */
export const TODOLISTS_ERROR_CODES = {
  // Project errors (13000-13019)
  PROJECT_NOT_FOUND: new ErrorCode(13000, 'Không tìm thấy dự án', HttpStatus.NOT_FOUND),
  PROJECT_CREATION_FAILED: new ErrorCode(13001, 'Tạo dự án thất bại', HttpStatus.BAD_REQUEST),
  PROJECT_UPDATE_FAILED: new ErrorCode(13002, 'Cập nhật dự án thất bại', HttpStatus.BAD_REQUEST),
  PROJECT_DELETE_FAILED: new ErrorCode(13003, 'Xóa dự án thất bại', HttpStatus.BAD_REQUEST),
  PROJECT_MEMBER_NOT_FOUND: new ErrorCode(13004, 'Không tìm thấy thành viên dự án', HttpStatus.NOT_FOUND),
  PROJECT_MEMBER_ALREADY_EXISTS: new ErrorCode(13005, 'Thành viên đã tồn tại trong dự án', HttpStatus.BAD_REQUEST),
  NOT_PROJECT_OWNER: new ErrorCode(13006, 'Bạn không phải là chủ sở hữu dự án', HttpStatus.FORBIDDEN),
  NOT_PROJECT_MEMBER: new ErrorCode(13007, 'Bạn không phải là thành viên dự án', HttpStatus.FORBIDDEN),

  // Todo errors (13020-13039)
  TODO_NOT_FOUND: new ErrorCode(13020, 'Không tìm thấy công việc', HttpStatus.NOT_FOUND),
  TODO_CREATION_FAILED: new ErrorCode(13021, 'Tạo công việc thất bại', HttpStatus.BAD_REQUEST),
  TODO_UPDATE_FAILED: new ErrorCode(13022, 'Cập nhật công việc thất bại', HttpStatus.BAD_REQUEST),
  TODO_DELETE_FAILED: new ErrorCode(13023, 'Xóa công việc thất bại', HttpStatus.BAD_REQUEST),
  TODO_STATUS_UPDATE_FAILED: new ErrorCode(13024, 'Cập nhật trạng thái công việc thất bại', HttpStatus.BAD_REQUEST),
  NOT_TODO_ASSIGNEE: new ErrorCode(13025, 'Bạn không phải là người được giao công việc này', HttpStatus.FORBIDDEN),
  INVALID_TODO_STATUS: new ErrorCode(13026, 'Trạng thái công việc không hợp lệ', HttpStatus.BAD_REQUEST),
  PARENT_TODO_NOT_FOUND: new ErrorCode(13027, 'Không tìm thấy công việc cha', HttpStatus.NOT_FOUND),

  // Comment errors (13040-13049)
  COMMENT_NOT_FOUND: new ErrorCode(13040, 'Không tìm thấy bình luận', HttpStatus.NOT_FOUND),
  COMMENT_CREATION_FAILED: new ErrorCode(13041, 'Tạo bình luận thất bại', HttpStatus.BAD_REQUEST),
  COMMENT_UPDATE_FAILED: new ErrorCode(13042, 'Cập nhật bình luận thất bại', HttpStatus.BAD_REQUEST),
  COMMENT_DELETE_FAILED: new ErrorCode(13043, 'Xóa bình luận thất bại', HttpStatus.BAD_REQUEST),
  NOT_COMMENT_OWNER: new ErrorCode(13044, 'Bạn không phải là người tạo bình luận này', HttpStatus.FORBIDDEN),
  COMMENT_FETCH_FAILED: new ErrorCode(13045, 'Lấy danh sách bình luận thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  COMMENT_DELETION_FAILED: new ErrorCode(13046, 'Xóa bình luận thất bại', HttpStatus.BAD_REQUEST),
  SYSTEM_EVENT_CREATION_FAILED: new ErrorCode(13047, 'Tạo sự kiện hệ thống thất bại', HttpStatus.BAD_REQUEST),

  // Attachment errors (13050-13059)
  ATTACHMENT_NOT_FOUND: new ErrorCode(13050, 'Không tìm thấy tệp đính kèm', HttpStatus.NOT_FOUND),
  ATTACHMENT_CREATION_FAILED: new ErrorCode(13051, 'Tạo tệp đính kèm thất bại', HttpStatus.BAD_REQUEST),
  ATTACHMENT_DELETE_FAILED: new ErrorCode(13052, 'Xóa tệp đính kèm thất bại', HttpStatus.BAD_REQUEST),
  NOT_ATTACHMENT_OWNER: new ErrorCode(13053, 'Bạn không phải là người tải lên tệp đính kèm này', HttpStatus.FORBIDDEN),
  ATTACHMENT_DELETION_FAILED: new ErrorCode(13054, 'Xóa tệp đính kèm thất bại', HttpStatus.BAD_REQUEST),
  ATTACHMENT_FETCH_FAILED: new ErrorCode(13055, 'Lấy danh sách tệp đính kèm thất bại', HttpStatus.INTERNAL_SERVER_ERROR),

  // Collaborator errors (13060-13069)
  COLLABORATOR_NOT_FOUND: new ErrorCode(13060, 'Không tìm thấy cộng tác viên', HttpStatus.NOT_FOUND),
  COLLABORATOR_ALREADY_EXISTS: new ErrorCode(13061, 'Người dùng đã là cộng tác viên của công việc này', HttpStatus.BAD_REQUEST),
  COLLABORATOR_CREATION_FAILED: new ErrorCode(13062, 'Thêm cộng tác viên thất bại', HttpStatus.BAD_REQUEST),
  COLLABORATOR_DELETION_FAILED: new ErrorCode(13063, 'Xóa cộng tác viên thất bại', HttpStatus.BAD_REQUEST),
  COLLABORATOR_FETCH_FAILED: new ErrorCode(13064, 'Lấy danh sách cộng tác viên thất bại', HttpStatus.INTERNAL_SERVER_ERROR),

  // Tag errors (13070-13079)
  TAG_NOT_FOUND: new ErrorCode(13070, 'Không tìm thấy nhãn', HttpStatus.NOT_FOUND),
  TAG_ALREADY_EXISTS: new ErrorCode(13071, 'Nhãn đã được gắn với công việc này', HttpStatus.BAD_REQUEST),
  TAG_CREATION_FAILED: new ErrorCode(13072, 'Thêm nhãn thất bại', HttpStatus.BAD_REQUEST),
  TAG_DELETE_FAILED: new ErrorCode(13073, 'Xóa nhãn thất bại', HttpStatus.BAD_REQUEST),

  // Appreciation errors (13080-13089)
  APPRECIATION_NOT_FOUND: new ErrorCode(13080, 'Không tìm thấy đánh giá', HttpStatus.NOT_FOUND),
  APPRECIATION_ALREADY_EXISTS: new ErrorCode(13081, 'Bạn đã đánh giá công việc này', HttpStatus.BAD_REQUEST),
  APPRECIATION_CREATION_FAILED: new ErrorCode(13082, 'Tạo đánh giá thất bại', HttpStatus.BAD_REQUEST),
  APPRECIATION_UPDATE_FAILED: new ErrorCode(13083, 'Cập nhật đánh giá thất bại', HttpStatus.BAD_REQUEST),
  APPRECIATION_DELETE_FAILED: new ErrorCode(13084, 'Xóa đánh giá thất bại', HttpStatus.BAD_REQUEST),
  NOT_APPRECIATION_OWNER: new ErrorCode(13085, 'Bạn không phải là người tạo đánh giá này', HttpStatus.FORBIDDEN),

  // TodoScore errors (13086-13089)
  TODO_NOT_COMPLETED: new ErrorCode(13086, 'Công việc chưa hoàn thành', HttpStatus.BAD_REQUEST),
  NOT_AUTHORIZED_TO_SCORE: new ErrorCode(13087, 'Không có quyền chấm điểm', HttpStatus.FORBIDDEN),
  TODO_SCORING_FAILED: new ErrorCode(13088, 'Chấm điểm công việc thất bại', HttpStatus.BAD_REQUEST),
  TODO_SCORE_HISTORY_FAILED: new ErrorCode(13089, 'Lấy lịch sử chấm điểm thất bại', HttpStatus.BAD_REQUEST),

  // Statistics errors (13095-13099)
  TODO_STATISTICS_FAILED: new ErrorCode(13095, 'Lấy thống kê thất bại', HttpStatus.BAD_REQUEST),

  // TaskKr errors (13090-13099)
  TASK_KR_NOT_FOUND: new ErrorCode(13090, 'Không tìm thấy liên kết giữa công việc và kết quả chính', HttpStatus.NOT_FOUND),
  TASK_KR_ALREADY_EXISTS: new ErrorCode(13091, 'Công việc đã được liên kết với kết quả chính này', HttpStatus.BAD_REQUEST),
  TASK_KR_CREATION_FAILED: new ErrorCode(13092, 'Liên kết công việc với kết quả chính thất bại', HttpStatus.BAD_REQUEST),
  TASK_KR_DELETE_FAILED: new ErrorCode(13093, 'Hủy liên kết công việc với kết quả chính thất bại', HttpStatus.BAD_REQUEST),
  KR_NOT_FOUND: new ErrorCode(13094, 'Không tìm thấy kết quả chính', HttpStatus.NOT_FOUND),
};
