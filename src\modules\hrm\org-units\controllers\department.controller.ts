import { Controller, Get, Post, Put, Delete, Body, Param, Query, ParseIntPipe, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { DepartmentService } from '../services/department.service';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { 
  CreateDepartmentDto, 
  UpdateDepartmentDto, 
  DepartmentQueryDto, 
  DepartmentResponseDto 
} from '../dto/department';
import { ApiResponseDto, PaginatedResult } from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';

/**
 * Controller for department operations
 */
@ApiTags(SWAGGER_API_TAG.EMPLOYEE)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('api/hrm/departments')
export class DepartmentController {
  constructor(private readonly departmentService: DepartmentService) {}

  /**
   * Create a new department
   * @param user Current user
   * @param dto Department creation DTO
   * @returns Created department
   */
  @Post()
  @ApiOperation({ summary: 'Tạo phòng ban mới' })
  @ApiResponse({
    status: 201,
    description: 'Phòng ban đã được tạo thành công.',
    type: () => ApiResponseDto,
  })
  async create(
    @CurrentUser() user: JwtPayload,
    @Body() dto: CreateDepartmentDto,
  ): Promise<ApiResponseDto<DepartmentResponseDto>> {
    const department = await this.departmentService.create(dto);
    return ApiResponseDto.created(department);
  }

  /**
   * Get all departments with pagination and filtering
   * @param user Current user
   * @param query Query parameters
   * @returns Paginated list of departments
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách phòng ban' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách phòng ban.',
    type: () => ApiResponseDto,
  })
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query() query: DepartmentQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<DepartmentResponseDto>>> {
    const departments = await this.departmentService.findAll(query);
    return ApiResponseDto.paginated(departments);
  }

  /**
   * Get department by ID
   * @param user Current user
   * @param id Department ID
   * @returns Department
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin phòng ban theo ID' })
  @ApiParam({ name: 'id', description: 'ID của phòng ban' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin phòng ban.',
    type: () => ApiResponseDto,
  })
  async findById(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<DepartmentResponseDto>> {
    const department = await this.departmentService.findById(id);
    return ApiResponseDto.success(department);
  }

  /**
   * Update department
   * @param user Current user
   * @param id Department ID
   * @param dto Update DTO
   * @returns Updated department
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật phòng ban' })
  @ApiParam({ name: 'id', description: 'ID của phòng ban' })
  @ApiResponse({
    status: 200,
    description: 'Phòng ban đã được cập nhật thành công.',
    type: () => ApiResponseDto,
  })
  async update(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateDepartmentDto,
  ): Promise<ApiResponseDto<DepartmentResponseDto>> {
    const department = await this.departmentService.update(id, dto);
    return ApiResponseDto.success(department);
  }

  /**
   * Delete department
   * @param user Current user
   * @param id Department ID
   * @returns Success message
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa phòng ban' })
  @ApiParam({ name: 'id', description: 'ID của phòng ban' })
  @ApiResponse({
    status: 200,
    description: 'Phòng ban đã được xóa thành công.',
    type: () => ApiResponseDto,
  })
  async delete(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<null>> {
    await this.departmentService.delete(id);
    return ApiResponseDto.deleted();
  }
} 