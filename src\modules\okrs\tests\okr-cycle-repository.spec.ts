import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { ConfigModule, ConfigService, ConfigType, DatabaseConfig } from '@/config';
import { OkrCycle } from '../entities/okr-cycle.entity';
import { Logger } from '@nestjs/common';
import { OkrCycleStatus } from '../enum/okr-cycle-status.enum';
import { OkrCycleRepository } from '../repositories/okr-cycle.repository';
import { OkrCycleQueryDto } from '../dto/okr-cycle';

/**
 * Test OkrCycleRepository với kết nối database thực
 */
describe('OkrCycleRepository', () => {
  let module: TestingModule;
  let dataSource: DataSource;
  let repository: OkrCycleRepository;
  const logger = new Logger('OkrCycleRepositoryTest');
  let testCycleId: number;

  beforeAll(async () => {
    // Tạo testing module với TypeOrmModule
    module = await Test.createTestingModule({
      imports: [
        // Import ConfigModule để lấy cấu hình database
        ConfigModule,
        // Import TypeOrmModule với cấu hình từ ConfigService
        TypeOrmModule.forRootAsync({
          inject: [ConfigService],
          useFactory: (configService: ConfigService) => {
            const dbConfig = configService.getConfig<DatabaseConfig>(ConfigType.Database);
            logger.log(`Connecting to database: ${dbConfig.database} on ${dbConfig.host}:${dbConfig.port}`);
            
            return {
              type: 'postgres',
              host: dbConfig.host,
              port: dbConfig.port,
              username: dbConfig.username,
              password: dbConfig.password,
              database: dbConfig.database,
              entities: [OkrCycle],
              synchronize: false,
              ssl: {
                rejectUnauthorized: !dbConfig.ssl,
              },
            };
          },
        }),
        // Import TypeOrmModule.forFeature để sử dụng repository
        TypeOrmModule.forFeature([OkrCycle]),
      ],
      providers: [OkrCycleRepository],
    }).compile();

    // Lấy DataSource và Repository
    dataSource = module.get<DataSource>(DataSource);
    repository = module.get<OkrCycleRepository>(OkrCycleRepository);

    // Tạo dữ liệu test
    await createTestData();
  });

  afterAll(async () => {
    // Xóa dữ liệu test
    await cleanupTestData();
    
    // Đóng kết nối sau khi test xong
    if (dataSource && dataSource.isInitialized) {
      await dataSource.destroy();
    }
    await module.close();
  });

  /**
   * Tạo dữ liệu test
   */
  async function createTestData() {
    try {
      // Tạo một chu kỳ OKR test
      const testCycle = {
        name: 'Test Cycle Repository',
        startDate: new Date('2025-01-01'),
        endDate: new Date('2025-03-31'),
        status: OkrCycleStatus.PLANNING,
        createdAt: Date.now(),
        tenantId: 999, // Sử dụng tenant ID test
      };

      const createdCycle = await repository.create(testCycle);
      testCycleId = createdCycle.id;
      logger.log(`Created test OkrCycle with ID: ${testCycleId}`);
    } catch (error) {
      logger.error(`Failed to create test data: ${error.message}`);
      throw error;
    }
  }

  /**
   * Xóa dữ liệu test
   */
  async function cleanupTestData() {
    try {
      if (testCycleId) {
        await repository.delete(testCycleId);
        logger.log(`Deleted test OkrCycle with ID: ${testCycleId}`);
      }
    } catch (error) {
      logger.error(`Failed to cleanup test data: ${error.message}`);
    }
  }

  it('should be defined', () => {
    expect(repository).toBeDefined();
  });

  it('should find a cycle by ID', async () => {
    // Tìm chu kỳ theo ID
    const cycle = await repository.findById(testCycleId);
    
    expect(cycle).toBeDefined();
    expect(cycle.id).toBe(testCycleId);
    expect(cycle.name).toBe('Test Cycle Repository');
  });

  it('should find all cycles with pagination', async () => {
    // Tìm tất cả chu kỳ với phân trang
    const query = new OkrCycleQueryDto();
    query.page = 1;
    query.limit = 10;
    
    const result = await repository.findAll(query);
    
    expect(result).toBeDefined();
    expect(result.items).toBeDefined();
    expect(result.meta).toBeDefined();
    expect(result.meta.totalItems).toBeGreaterThanOrEqual(1);
    expect(result.meta.currentPage).toBe(1);
  });

  it('should update a cycle', async () => {
    // Cập nhật chu kỳ
    const updatedCycle = await repository.update(testCycleId, {
      name: 'Updated Test Cycle',
    });
    
    expect(updatedCycle).toBeDefined();
    expect(updatedCycle.id).toBe(testCycleId);
    expect(updatedCycle.name).toBe('Updated Test Cycle');
  });

  it('should find active cycles', async () => {
    // Cập nhật chu kỳ thành active
    await repository.update(testCycleId, {
      status: OkrCycleStatus.ACTIVE,
    });
    
    // Tìm chu kỳ active
    const activeCycle = await repository.findActive();
    
    // Có thể có nhiều chu kỳ active, nên chỉ kiểm tra xem có tìm thấy không
    expect(activeCycle).toBeDefined();
    expect(activeCycle.status).toBe(OkrCycleStatus.ACTIVE);
  });
});
