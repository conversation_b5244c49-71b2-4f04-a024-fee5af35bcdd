import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for key result update response
 */
export class KeyResultUpdateResponseDto {
  /**
   * Unique identifier for the key result update record
   * @example 1
   */
  @ApiProperty({
    description: 'ID của bản ghi cập nhật',
    example: 1,
  })
  id: number;

  /**
   * ID of the key result being updated
   * @example 1
   */
  @ApiProperty({
    description: 'ID của kết quả chính',
    example: 1,
    nullable: true,
  })
  keyResultId: number | null;

  /**
   * Previous value of the key result before the update
   * @example 500
   */
  @ApiProperty({
    description: 'Gi<PERSON> trị trước khi cập nhật',
    example: 500,
  })
  previousValue: number;

  /**
   * New value of the key result after the update
   * @example 800
   */
  @ApiProperty({
    description: 'Giá trị mới sau khi cập nhật',
    example: 800,
  })
  newValue: number;

  /**
   * ID of the user who performed the update
   * @example 1
   */
  @ApiProperty({
    description: 'ID của người cập nhật',
    example: 1,
  })
  updateBy: number;

  /**
   * Confidence level for completing the key result (1-5)
   * @example 4
   */
  @ApiProperty({
    description: 'Mức độ tự tin (1-5)',
    example: 4,
    nullable: true,
  })
  confidenceLevel: number | null;

  /**
   * Notes for the update
   * @example "Đã hoàn thành chiến dịch marketing trên Facebook, tăng thêm 300 khách hàng mới"
   */
  @ApiProperty({
    description: 'Ghi chú cập nhật',
    example: 'Đã hoàn thành chiến dịch marketing trên Facebook, tăng thêm 300 khách hàng mới',
    nullable: true,
  })
  notes: string | null;

  /**
   * Type of check-in
   * @example "manual"
   */
  @ApiProperty({
    description: 'Loại check-in',
    example: 'manual',
    nullable: true,
  })
  checkInType: string | null;

  /**
   * Update timestamp (in milliseconds)
   * @example 1672617600000
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (tính bằng mili giây)',
    example: 1672617600000,
    nullable: true,
  })
  updatedDate: number | null;
}
