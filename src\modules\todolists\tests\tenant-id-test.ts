import { createConnection, DataSource } from 'typeorm';
import { config } from 'dotenv';
import { Todo } from '../entities/todo.entity';
import { TodoPriority } from '../enum/todo-priority.enum';
import { tenantContext } from '@/common/subscribers/tenant-entity.subscriber';

// Đọc biến môi trường từ file .env
config();

/**
 * Script để kiểm tra việc tạo Todo với tenantId
 */
async function testTenantIdInTodo() {
  console.log('Starting tenant ID test for Todo entity...');

  let dataSource: DataSource | null = null;

  try {
    // Kết nối đến database
    dataSource = new DataSource({
      type: 'postgres',
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432', 10),
      username: process.env.DB_USERNAME || 'postgres',
      password: process.env.DB_PASSWORD || 'postgres',
      database: process.env.DB_DATABASE || 'postgres',
      entities: [Todo],
      synchronize: false,
      ssl: process.env.DB_SSL === 'true' ? {
        rejectUnauthorized: false,
      } : false,
      logging: ['query', 'error'],
    });

    await dataSource.initialize();
    console.log('Connected to database successfully');

    // Lấy repository cho Todo
    const todoRepository = dataSource.getRepository(Todo);

    // Kiểm tra cấu trúc bảng todos
    console.log('Checking todos table structure...');
    const tableInfo = await dataSource.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'todos'
      ORDER BY ordinal_position
    `);

    console.log('Todos table structure:');
    console.table(tableInfo);

    // Kiểm tra ràng buộc khóa ngoại
    console.log('Checking foreign key constraints...');
    const fkConstraints = await dataSource.query(`
      SELECT
        tc.constraint_name,
        tc.table_name,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
      FROM
        information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_name = 'todos'
    `);

    console.log('Foreign key constraints:');
    console.table(fkConstraints);

    // Kiểm tra dữ liệu hiện có
    console.log('Checking existing todos data...');
    const existingTodos = await todoRepository.find({ take: 5 });

    console.log(`Found ${existingTodos.length} todos`);
    if (existingTodos.length > 0) {
      console.log('Sample todos:');
      existingTodos.forEach(todo => {
        console.log(`ID: ${todo.id}, Title: ${todo.title}, TenantId: ${todo.tenantId}`);
      });
    }

    // Kiểm tra companies table
    console.log('Checking companies table...');
    try {
      const companies = await dataSource.query(`
        SELECT id, name FROM companies LIMIT 5
      `);

      console.log(`Found ${companies.length} companies`);
      if (companies.length > 0) {
        console.log('Sample companies:');
        console.table(companies);
      }
    } catch (error) {
      console.error('Error querying companies table:', error.message);
    }

    // Thử tạo Todo với tenantId
    console.log('Testing Todo creation with tenantId...');

    // Lấy một tenantId hợp lệ từ bảng companies
    let validTenantId: number = 999; // Giá trị mặc định
    try {
      const companies = await dataSource.query(`
        SELECT id FROM companies LIMIT 1
      `);

      if (companies.length > 0) {
        validTenantId = parseInt(companies[0].id, 10);
        console.log(`Using valid tenantId: ${validTenantId}`);
      } else {
        console.warn('No companies found, using test tenantId');
      }
    } catch (error) {
      console.error('Error getting valid tenantId:', error.message);
    }

    // Tạo Todo với tenantId trong context
    await tenantContext.run({ tenantId: validTenantId }, async () => {
      try {
        // Tạo một Todo mới
        const newTodo = todoRepository.create({
          title: 'Test Todo with TenantId',
          description: 'This is a test todo to check tenantId',
          assigneeId: 1,
          priority: TodoPriority.MEDIUM,
          expectedStars: 3,
        });

        console.log('Todo before save:', newTodo);

        // Lưu Todo
        const savedTodo = await todoRepository.save(newTodo);
        console.log('Todo after save:', savedTodo);

        // Kiểm tra xem tenantId có được tự động thêm vào không
        if (savedTodo.tenantId === validTenantId) {
          console.log(`Success: tenantId ${validTenantId} was automatically added to the Todo entity`);
        } else {
          console.warn(`Warning: tenantId was not set correctly. Expected: ${validTenantId}, Actual: ${savedTodo.tenantId}`);
        }

        // Xóa Todo test
        await todoRepository.delete(savedTodo.id);
        console.log(`Deleted test Todo with ID: ${savedTodo.id}`);
      } catch (error) {
        console.error('Error creating Todo:', error.message);
      }
    });

  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Đóng kết nối
    if (dataSource && dataSource.isInitialized) {
      await dataSource.destroy();
      console.log('Connection closed');
    }
  }
}

// Chạy test
testTenantIdInTodo().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
