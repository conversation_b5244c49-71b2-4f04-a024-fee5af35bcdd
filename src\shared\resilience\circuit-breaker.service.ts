import { Injectable, Logger } from '@nestjs/common';
import * as CircuitBreaker from 'opossum';

@Injectable()
export class CircuitBreakerService {
  private readonly logger = new Logger(CircuitBreakerService.name);

  createBreaker(name: string, options: CircuitBreaker.Options = {}): CircuitBreaker {
    const breaker = new CircuitBreaker(async (fn: () => Promise<any>) => fn(), {
      name,
      ...options,
    });

    breaker.on('open', () => {
      this.logger.warn(`Circuit Breaker ${name} is now OPEN`);
    });

    breaker.on('halfOpen', () => {
      this.logger.log(`Circuit Breaker ${name} is now HALF-OPEN`);
    });

    breaker.on('close', () => {
      this.logger.log(`Circuit Breaker ${name} is now CLOSED`);
    });

    return breaker;
  }
}