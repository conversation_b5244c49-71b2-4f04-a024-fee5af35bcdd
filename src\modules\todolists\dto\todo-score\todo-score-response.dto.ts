import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi thông tin chấm điểm công việc
 */
export class TodoScoreResponseDto {
  /**
   * ID của bản ghi chấm điểm
   * @example 1
   */
  @ApiProperty({
    description: 'ID của bản ghi chấm điểm',
    example: 1,
  })
  id: number;

  /**
   * ID của công việc được chấm điểm
   * @example 42
   */
  @ApiProperty({
    description: 'ID của công việc được chấm điểm',
    example: 42,
  })
  todoId: number;

  /**
   * ID của người chấm điểm
   * @example 5
   */
  @ApiProperty({
    description: 'ID của người chấm điểm',
    example: 5,
  })
  scorerId: number;

  /**
   * Số sao đánh giá thực tế (1-5)
   * @example 4
   */
  @ApiProperty({
    description: 'Số sao đánh gi<PERSON> thực tế (1-5)',
    example: 4,
  })
  awardedStars: number;

  /**
   * <PERSON>ản hồi về công việc
   * @example 'Hoàn thành tốt nhiệm vụ, nhưng còn chậm tiến độ'
   */
  @ApiProperty({
    description: 'Phản hồi về công việc',
    example: 'Hoàn thành tốt nhiệm vụ, nhưng còn chậm tiến độ',
    nullable: true,
  })
  feedback: string | null;

  /**
   * Thời gian chấm điểm (timestamp)
   * @example 1625097600000
   */
  @ApiProperty({
    description: 'Thời gian chấm điểm (timestamp)',
    example: 1625097600000,
    nullable: true,
  })
  createdAt: number | null;
}
