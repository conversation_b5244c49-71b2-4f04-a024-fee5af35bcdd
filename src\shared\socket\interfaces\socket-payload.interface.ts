/**
 * Interface định nghĩa cấu trúc dữ liệu cơ bản cho các sự kiện Socket.IO
 */
export interface SocketPayload<T = any> {
  /**
   * Loại sự kiện
   */
  event: string;
  
  /**
   * Dữ liệu của sự kiện
   */
  data: T;
  
  /**
   * Thời gian tạo sự kiện
   */
  timestamp?: number;
  
  /**
   * ID của người gửi
   */
  senderId?: string;
  
  /**
   * ID của phòng (nếu có)
   */
  roomId?: string;
  
  /**
   * ID của tin nhắn (nếu có)
   */
  messageId?: string;
  
  /**
   * Mã theo dõi để xác định request-response
   */
  traceId?: string;
}
