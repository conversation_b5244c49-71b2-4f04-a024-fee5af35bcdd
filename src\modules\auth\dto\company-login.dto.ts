import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';

/**
 * DTO cho đăng nhập tài khoản công ty
 */
export class CompanyLoginDto {
  @ApiProperty({
    description: 'Email đăng nhập',
    example: '<EMAIL>',
  })
  @IsNotEmpty({ message: 'Email không được để trống' })
  @IsEmail({}, { message: 'Email không hợp lệ' })
  email: string;

  @ApiProperty({
    description: 'Mật khẩu đăng nhập',
    example: 'StrongPassword123!',
  })
  @IsNotEmpty({ message: 'Mật khẩu không được để trống' })
  @IsString({ message: 'Mật khẩu phải là chuỗi' })
  password: string;

  @ApiProperty({
    description: 'Token reCAPTCHA từ client',
    example: '03AGdBq24PBCbwiDRgMN...',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Token reCAPTCHA phải là chuỗi' })
  recaptchaToken?: string;
}
