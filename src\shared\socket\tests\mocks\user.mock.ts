import { JwtPayload, TokenType } from '../../interfaces/jwt-payload.interface';

/**
 * <PERSON><PERSON> cho thông tin người dùng
 */
export const mockUser: JwtPayload = {
  id: 123,
  sub: 123,
  username: 'testuser',
  permissions: ['user:read', 'user:write'],
  typeToken: TokenType.ACCESS,
  tenantId: 123,
  domain: 'example.com',
  type: 'EMPLOYEE',
};

/**
 * <PERSON><PERSON> cho thông tin người dùng admin
 */
export const mockAdminUser: JwtPayload = {
  id: 456,
  sub: 456,
  username: 'adminuser',
  permissions: ['*'],
  typeToken: TokenType.ACCESS,
  tenantId: 123,
  domain: 'example.com',
  type: 'COMPANY_ADMIN',
};

/**
 * Mock cho thông tin người dùng hệ thống
 */
export const mockSystemUser: JwtPayload = {
  id: 789,
  sub: 789,
  username: 'systemuser',
  permissions: ['*'],
  typeToken: TokenType.ACCESS,
  tenantId: 123,
  domain: 'example.com',
  type: 'SYSTEM_ADMIN',
};
