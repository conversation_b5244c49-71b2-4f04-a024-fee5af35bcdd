import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsInt, IsNotEmpty, Min } from 'class-validator';
import { ProjectMemberRole } from '../../enum/project-member-role.enum';

/**
 * DTO cho thêm thành viên vào dự án
 */
export class CreateProjectMemberDto {
  /**
   * ID người dùng
   * @example 1
   */
  @ApiProperty({
    description: 'ID người dùng',
    example: 1,
    required: true,
  })
  @IsNotEmpty({ message: 'ID người dùng không được để trống' })
  @IsInt({ message: 'ID người dùng phải là số nguyên' })
  @Min(1, { message: 'ID người dùng phải lớn hơn 0' })
  userId: number;

  /**
   * Vai trò của thành viên trong dự án
   * @example "member"
   */
  @ApiProperty({
    description: '<PERSON>ai trò của thành viên trong dự án',
    enum: ProjectMemberRole,
    example: ProjectMemberRole.MEMBER,
    required: true,
  })
  @IsNotEmpty({ message: '<PERSON>ai trò không được để trống' })
  @IsEnum(ProjectMemberRole, { message: 'Vai trò không hợp lệ' })
  role: ProjectMemberRole;
}
