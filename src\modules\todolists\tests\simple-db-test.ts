import { createConnection } from 'typeorm';
import { config } from 'dotenv';
import { Todo } from '../entities/todo.entity';
import { Project } from '../entities/project.entity';
import { ProjectMember } from '../entities/project-members.entity';
import { TodoTag } from '../entities/todo-tag.entity';
import { TaskKr } from '../entities/task-kr.entity';

// Đọc biến môi trường từ file .env
config();

/**
 * Script đơn giản để kiểm tra kết nối database
 */
async function testDatabaseConnection() {
  console.log('Starting simple database connection test...');

  try {
    // Lấy thông tin kết nối từ biến môi trường
    const host = process.env.DB_HOST || 'localhost';
    const port = parseInt(process.env.DB_PORT || '5432', 10);
    const username = process.env.DB_USERNAME || 'postgres';
    const password = process.env.DB_PASSWORD || 'postgres';
    const database = process.env.DB_DATABASE || 'postgres';
    const ssl = process.env.DB_SSL === 'true';

    console.log(`Connecting to database: ${database} on ${host}:${port}`);
    console.log(`SSL enabled: ${ssl}`);

    // Tạo kết nối với database
    const connection = await createConnection({
      type: 'postgres',
      host,
      port,
      username,
      password,
      database,
      entities: [Todo, Project, ProjectMember, TodoTag, TaskKr],
      synchronize: false,
      ssl: ssl ? { rejectUnauthorized: false } : false,
    });

    console.log('Database connection established successfully');

    // Thực hiện truy vấn đơn giản
    const result = await connection.query('SELECT 1 as value');
    console.log(`Query result: ${JSON.stringify(result)}`);

    // Đóng kết nối
    await connection.close();
    console.log('Database connection closed');

  } catch (error) {
    console.error(`Database connection test failed: ${error.message}`);
    console.error(error);
  }
}

// Chạy test
testDatabaseConnection();
