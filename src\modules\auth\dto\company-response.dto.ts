import { ApiProperty } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';
import { CompanyStatus } from '../enum/company-status.enum';

/**
 * DTO cho phản hồi thông tin công ty
 */
@Exclude()
export class CompanyResponseDto {
  @Expose()
  @ApiProperty({
    description: 'ID của công ty',
    example: 1672531200000,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Tên công ty',
    example: 'Công ty TNHH ABC',
  })
  companyName: string;

  @Expose()
  @ApiProperty({
    description: 'Subdomain của công ty',
    example: '0123456789',
  })
  subdomain: string;

  @Expose()
  @ApiProperty({
    description: 'Mã số thuế của công ty',
    example: '0123456789',
  })
  taxCode: string;

  @Expose()
  @ApiProperty({
    description: '<PERSON><PERSON> liên hệ của công ty',
    example: '<EMAIL>',
  })
  companyEmail: string;

  @Expose()
  @ApiProperty({
    description: 'Số điện thoại liên hệ của công ty',
    example: '0912345678',
    nullable: true,
  })
  phoneNumber: string | null;

  @Expose()
  @ApiProperty({
    description: 'Địa chỉ công ty',
    example: '123 Đường ABC, Quận 1, TP.HCM',
    nullable: true,
  })
  address: string | null;

  @Expose()
  @ApiProperty({
    description: 'Trạng thái tài khoản công ty',
    enum: CompanyStatus,
    example: CompanyStatus.ACTIVE,
  })
  status: CompanyStatus;

  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo tài khoản (timestamp)',
    example: 1672531200000,
  })
  createdAt: number;

  @Expose()
  @ApiProperty({
    description: 'Thời gian cập nhật gần nhất (timestamp)',
    example: 1672531200000,
    nullable: true,
  })
  updatedAt: number | null;

  constructor(partial: Partial<CompanyResponseDto>) {
    Object.assign(this, partial);
  }
}

/**
 * DTO cho phản hồi đăng ký công ty thành công
 */
export class CompanyRegisterResponseDto {
  @ApiProperty({
    description: 'Thông báo đăng ký thành công',
    example: 'Đăng ký tài khoản thành công. Vui lòng kiểm tra email để xác thực tài khoản.',
  })
  message: string;

  @ApiProperty({
    description: 'Token OTP để xác thực',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    required: false,
  })
  otpToken?: string;

  @ApiProperty({
    description: 'Thời gian hết hạn của OTP (timestamp)',
    example: 1672531200000,
    required: false,
  })
  expiresAt?: number;

  @ApiProperty({
    description: 'Email đã được che một phần để hiển thị',
    example: 'j***@example.com',
    required: false,
  })
  maskedEmail?: string;

  @ApiProperty({
    description: 'Mã OTP (chỉ trả về trong môi trường development)',
    example: '123456',
    required: false,
  })
  otp?: string;
}

/**
 * DTO cho phản hồi xác thực email thành công
 */
export class VerifyEmailResponseDto {
  @ApiProperty({
    description: 'Thông báo xác thực thành công',
    example: 'Xác thực email thành công. Tài khoản của bạn đã được kích hoạt.',
  })
  message: string;

  @ApiProperty({
    description: 'Token truy cập',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: 'Thông tin công ty',
    type: CompanyResponseDto,
  })
  company: CompanyResponseDto;

  @ApiProperty({
    description: 'Danh sách quyền của người dùng',
    example: ['user:create', 'user:read', 'user:update', 'user:delete'],
    type: [String],
  })
  permissions?: string[];
}

/**
 * DTO cho phản hồi đăng nhập thành công
 */
export class CompanyLoginResponseDto {
  @ApiProperty({
    description: 'Token truy cập',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: 'Thông tin công ty',
    type: CompanyResponseDto,
  })
  company: CompanyResponseDto;

  @ApiProperty({
    description: 'Danh sách quyền của người dùng',
    example: ['user:create', 'user:read', 'user:update', 'user:delete'],
    type: [String],
  })
  permissions?: string[];
}

/**
 * DTO cho phản hồi gửi lại OTP thành công
 */
export class ResendOtpResponseDto {
  @ApiProperty({
    description: 'Thông báo gửi lại OTP thành công',
    example: 'Mã OTP mới đã được gửi đến email của bạn.',
  })
  message: string;

  @ApiProperty({
    description: 'Token OTP để xác thực',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    required: false,
  })
  otpToken?: string;

  @ApiProperty({
    description: 'Thời gian hết hạn của OTP (timestamp)',
    example: 1672531200000,
    required: false,
  })
  expiresAt?: number;

  @ApiProperty({
    description: 'Email đã được che một phần để hiển thị',
    example: 'j***@example.com',
    required: false,
  })
  maskedEmail?: string;

  @ApiProperty({
    description: 'Mã OTP (chỉ trả về trong môi trường development)',
    example: '123456',
    required: false,
  })
  otp?: string;
}
