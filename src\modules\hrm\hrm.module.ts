import { Module } from '@nestjs/common';
import { OrgUnitsModule } from './org-units/org-units.module';
import { EmployeesModule } from './employees/employees.module';
import { ContractsModule } from './contracts/contracts.module';

@Module({
  imports: [OrgUnitsModule, EmployeesModule, ContractsModule],
  controllers: [],
  providers: [],
  exports: [OrgUnitsModule, EmployeesModule, ContractsModule],
})
export class HrmModule {}