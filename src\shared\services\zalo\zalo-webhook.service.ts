import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppException, ErrorCode } from '@common/exceptions';
import { ZaloWebhookEvent } from './zalo.interface';
import * as crypto from 'crypto';

@Injectable()
export class ZaloWebhookService {
  private readonly logger = new Logger(ZaloWebhookService.name);
  private readonly webhookSecret: string;

  constructor(private readonly configService: ConfigService) {
    const secret = this.configService.get<string>('ZALO_WEBHOOK_SECRET');
    if (!secret) {
      throw new Error('ZALO_WEBHOOK_SECRET is not defined in configuration');
    }
    this.webhookSecret = secret;
  }

  /**
   * Xác thực webhook từ Zalo
   * @param timestamp Thời gian gửi webhook
   * @param mac MAC (Message Authentication Code)
   * @param body Nội dung webhook
   * @returns Kết quả xác thực
   */
  verifyWebhook(timestamp: string, mac: string, body: string): boolean {
    try {
      if (!this.webhookSecret) {
        this.logger.warn('ZALO_WEBHOOK_SECRET is not configured');
        return false;
      }

      // Tạo chuỗi dữ liệu để tính toán MAC
      const data = `${timestamp}.${body}`;

      // Tính toán MAC bằng HMAC-SHA256
      const expectedMac = crypto
        .createHmac('sha256', this.webhookSecret)
        .update(data)
        .digest('hex');

      // So sánh MAC tính toán với MAC nhận được
      return mac === expectedMac;
    } catch (error) {
      this.logger.error(
        `Error verifying webhook: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Xử lý sự kiện webhook từ Zalo
   * @param event Sự kiện webhook
   * @returns Kết quả xử lý
   */
  async processWebhookEvent(
    event: ZaloWebhookEvent,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.log(`Processing webhook event: ${event.event_name}`);

      // Xử lý các loại sự kiện khác nhau
      switch (event.event_name) {
        case 'user_send_text':
          await this.processUserSendText(event);
          break;
        case 'user_send_image':
          await this.processUserSendImage(event);
          break;
        case 'user_send_file':
          await this.processUserSendFile(event);
          break;
        case 'user_follow':
          await this.processUserFollow(event);
          break;
        case 'user_unfollow':
          await this.processUserUnfollow(event);
          break;
        default:
          this.logger.log(`Unhandled event type: ${event.event_name}`);
      }

      return { success: true };
    } catch (error) {
      this.logger.error(
        `Error processing webhook event: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xử lý sự kiện webhook',
      );
    }
  }

  /**
   * Xử lý sự kiện người dùng gửi tin nhắn văn bản
   * @param event Sự kiện webhook
   */
  private async processUserSendText(event: ZaloWebhookEvent): Promise<void> {
    try {
      const { sender, message } = event.data;
      this.logger.log(`User ${sender.id} sent text: ${message.text}`);
      // Thực hiện xử lý tin nhắn văn bản
      // Ví dụ: Lưu tin nhắn vào cơ sở dữ liệu, chuyển tiếp tin nhắn đến agent, v.v.
    } catch (error) {
      this.logger.error(
        `Error processing user_send_text event: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Xử lý sự kiện người dùng gửi hình ảnh
   * @param event Sự kiện webhook
   */
  private async processUserSendImage(event: ZaloWebhookEvent): Promise<void> {
    try {
      const { sender, message } = event.data;
      this.logger.log(
        `User ${sender.id} sent image: ${message.attachments[0].payload.url}`,
      );
      // Thực hiện xử lý tin nhắn hình ảnh
    } catch (error) {
      this.logger.error(
        `Error processing user_send_image event: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Xử lý sự kiện người dùng gửi tệp đính kèm
   * @param event Sự kiện webhook
   */
  private async processUserSendFile(event: ZaloWebhookEvent): Promise<void> {
    try {
      const { sender, message } = event.data;
      this.logger.log(
        `User ${sender.id} sent file: ${message.attachments[0].payload.url}`,
      );
      // Thực hiện xử lý tin nhắn tệp đính kèm
    } catch (error) {
      this.logger.error(
        `Error processing user_send_file event: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Xử lý sự kiện người dùng theo dõi Official Account
   * @param event Sự kiện webhook
   */
  private async processUserFollow(event: ZaloWebhookEvent): Promise<void> {
    try {
      const { follower } = event.data;
      this.logger.log(`User ${follower.id} followed the OA`);
      // Thực hiện xử lý sự kiện theo dõi
    } catch (error) {
      this.logger.error(
        `Error processing user_follow event: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Xử lý sự kiện người dùng hủy theo dõi Official Account
   * @param event Sự kiện webhook
   */
  private async processUserUnfollow(event: ZaloWebhookEvent): Promise<void> {
    try {
      const { follower } = event.data;
      this.logger.log(`User ${follower.id} unfollowed the OA`);
      // Thực hiện xử lý sự kiện hủy theo dõi
    } catch (error) {
      this.logger.error(
        `Error processing user_unfollow event: ${error.message}`,
        error.stack,
      );
    }
  }
}
