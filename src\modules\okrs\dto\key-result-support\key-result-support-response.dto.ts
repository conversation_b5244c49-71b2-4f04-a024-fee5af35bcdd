import { ApiProperty } from '@nestjs/swagger';
import { KeyResultResponseDto } from '../key-result/key-result-response.dto';

/**
 * DTO for key result support response
 */
export class KeyResultSupportResponseDto {
  /**
   * Parent key result ID
   * @example 1
   */
  @ApiProperty({
    description: 'ID của kết quả chính chính',
    example: 1,
  })
  parentId: number;

  /**
   * Child key result ID
   * @example 2
   */
  @ApiProperty({
    description: 'ID của kết quả chính hỗ trợ',
    example: 2,
  })
  childId: number;

  /**
   * Child key result details
   */
  @ApiProperty({
    description: 'Chi tiết kết quả chính hỗ trợ',
    type: KeyResultResponseDto,
  })
  childKeyResult?: KeyResultResponseDto;
}
