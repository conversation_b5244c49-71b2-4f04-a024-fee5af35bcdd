import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TodoAttachment } from '../entities/todo-attachment.entity';
import { TodoAttachmentQueryDto } from '../dto/todo-attachment/todo-attachment-query.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';

/**
 * Repository xử lý truy vấn dữ liệu cho tệp đính kèm công việc
 */
@Injectable()
export class TodoAttachmentRepository {
  constructor(
    @InjectRepository(TodoAttachment)
    private readonly repository: Repository<TodoAttachment>,
  ) {}

  /**
   * Tạo mới tệp đính kèm
   * @param data Dữ liệu tệp đính kèm
   * @returns Tệp đính kèm đã tạo
   */
  async create(data: Partial<TodoAttachment>): Promise<TodoAttachment> {
    const attachment = this.repository.create(data);
    return this.repository.save(attachment);
  }

  /**
   * Tìm tệp đính kèm theo ID
   * @param id ID tệp đính kèm
   * @returns Tệp đính kèm nếu tìm thấy, null nếu không
   */
  async findById(id: number): Promise<TodoAttachment | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm tất cả tệp đính kèm với phân trang và lọc
   * @param query Tham số truy vấn
   * @returns Danh sách tệp đính kèm đã phân trang
   */
  async findAll(query: TodoAttachmentQueryDto): Promise<PaginatedResult<TodoAttachment>> {
    const { page = 1, limit = 10, todoId, createdBy, search, sortBy = 'createdAt', sortDirection = 'DESC' } = query;

    const queryBuilder = this.repository.createQueryBuilder('attachment');

    // Áp dụng bộ lọc todoId nếu được cung cấp
    if (todoId) {
      queryBuilder.andWhere('attachment.todoId = :todoId', { todoId });
    }

    // Áp dụng bộ lọc createdBy nếu được cung cấp
    if (createdBy) {
      queryBuilder.andWhere('attachment.createdBy = :createdBy', { createdBy });
    }

    // Áp dụng bộ lọc tìm kiếm nếu được cung cấp
    if (search) {
      queryBuilder.andWhere('attachment.filename ILIKE :search', {
        search: `%${search}%`,
      });
    }

    // Áp dụng sắp xếp
    queryBuilder.orderBy(`attachment.${sortBy}`, sortDirection);

    // Áp dụng phân trang
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm tất cả tệp đính kèm của một công việc
   * @param todoId ID công việc
   * @returns Danh sách tệp đính kèm
   */
  async findByTodoId(todoId: number): Promise<TodoAttachment[]> {
    return this.repository.find({
      where: { todoId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Xóa tệp đính kèm
   * @param id ID tệp đính kèm
   * @returns Kết quả xóa
   */
  async remove(id: number): Promise<void> {
    await this.repository.delete(id);
  }
}
