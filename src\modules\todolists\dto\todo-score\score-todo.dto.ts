import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional, IsString, <PERSON>, <PERSON>, <PERSON> } from 'class-validator';

/**
 * DTO cho chấm điểm công việc
 */
export class ScoreTodoDto {
  /**
   * Số sao đánh giá thực tế (1-5)
   * @example 4
   */
  @ApiProperty({
    description: 'Số sao đánh giá thực tế (1-5)',
    example: 4,
    required: true,
  })
  @IsInt({ message: 'Số sao phải là số nguyên' })
  @Min(1, { message: 'Số sao phải từ 1 đến 5' })
  @Max(5, { message: 'Số sao phải từ 1 đến 5' })
  awardedStars: number;

  /**
   * Phản hồi về công việc
   * @example 'Hoàn thành tốt nhiệm vụ, nhưng còn chậm tiến độ'
   */
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> hồi về công việc',
    example: '<PERSON><PERSON><PERSON> thành tốt nhiệm vụ, nhưng còn chậm tiến độ',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Phản hồi phải là chuỗi' })
  @MaxLength(500, { message: 'Phản hồi không được vượt quá 500 ký tự' })
  feedback?: string;
}
