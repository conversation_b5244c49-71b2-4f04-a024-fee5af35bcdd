import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@/common/exceptions/app.exception';

/**
 * Mã lỗi cho module HRM
 * Phạm vi: 13000-13099
 */
export const HRM_ERROR_CODES = {
  // Department errors (13000-13019)
  DEPARTMENT_NOT_FOUND: new ErrorCode(
    13000,
    'Không tìm thấy phòng ban',
    HttpStatus.NOT_FOUND,
  ),
  DEPARTMENT_NAME_ALREADY_EXISTS: new ErrorCode(
    13001,
    'Tên phòng ban đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),
  DEPARTMENT_CIRCULAR_REFERENCE: new ErrorCode(
    13002,
    'Phát hiện tham chiếu vòng tròn trong cấu trúc phòng ban',
    HttpStatus.BAD_REQUEST,
  ),
  DEPARTMENT_HAS_CHILDREN: new ErrorCode(
    13003,
    'Phòng ban có chứa phòng ban con, không thể xóa',
    HttpStatus.BAD_REQUEST,
  ),
  DEPARTMENT_PARENT_NOT_FOUND: new ErrorCode(
    13004,
    'Phòng ban cấp trên không tồn tại',
    HttpStatus.BAD_REQUEST,
  ),
  DEPARTMENT_MANAGER_NOT_FOUND: new ErrorCode(
    13005,
    'Người quản lý phòng ban không tồn tại',
    HttpStatus.BAD_REQUEST,
  ),

  // Employee errors (13020-13039)
  EMPLOYEE_NOT_FOUND: new ErrorCode(
    13020,
    'Không tìm thấy nhân viên',
    HttpStatus.NOT_FOUND,
  ),
  EMPLOYEE_CODE_EXISTS: new ErrorCode(
    13021,
    'Mã nhân viên đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),
  EMPLOYEE_CREATE_FAILED: new ErrorCode(
    13022,
    'Tạo nhân viên thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  EMPLOYEE_UPDATE_FAILED: new ErrorCode(
    13023,
    'Cập nhật nhân viên thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  EMPLOYEE_DELETE_FAILED: new ErrorCode(
    13024,
    'Xóa nhân viên thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  EMPLOYEE_FIND_FAILED: new ErrorCode(
    13025,
    'Tìm kiếm nhân viên thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  EMPLOYEE_SELF_MANAGER: new ErrorCode(
    13026,
    'Nhân viên không thể là quản lý của chính mình',
    HttpStatus.BAD_REQUEST,
  ),
  EMPLOYEE_TERMINATION_REASON_REQUIRED: new ErrorCode(
    13027,
    'Cần cung cấp lý do chấm dứt hợp đồng',
    HttpStatus.BAD_REQUEST,
  ),
  USERNAME_EXISTS: new ErrorCode(
    13028,
    'Tên đăng nhập đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),
  EMAIL_EXISTS: new ErrorCode(
    13029,
    'Địa chỉ email đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),
  USER_CREATION_FAILED: new ErrorCode(
    13030,
    'Tạo tài khoản người dùng thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  USER_NOT_FOUND: new ErrorCode(
    13031,
    'Không tìm thấy người dùng',
    HttpStatus.NOT_FOUND,
  ),
  PERMISSION_FETCH_FAILED: new ErrorCode(
    13032,
    'Không thể lấy danh sách quyền',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  ROLE_NOT_FOUND: new ErrorCode(
    13033,
    'Không tìm thấy vai trò',
    HttpStatus.NOT_FOUND,
  ),
  PERMISSION_NOT_FOUND: new ErrorCode(
    13034,
    'Không tìm thấy quyền',
    HttpStatus.NOT_FOUND,
  ),
  ROLE_UPDATE_FAILED: new ErrorCode(
    13035,
    'Cập nhật vai trò thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  PERMISSION_UPDATE_FAILED: new ErrorCode(
    13036,
    'Cập nhật quyền thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  ROLE_FETCH_FAILED: new ErrorCode(
    13037,
    'Không thể lấy danh sách vai trò',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  EMPLOYEE_ALREADY_HAS_USER: new ErrorCode(
    13038,
    'Nhân viên đã có tài khoản người dùng',
    HttpStatus.BAD_REQUEST,
  ),
  USER_ALREADY_HAS_EMPLOYEE: new ErrorCode(
    13039,
    'Người dùng đã gắn với nhân viên khác',
    HttpStatus.BAD_REQUEST,
  ),

  // Contract errors (13040-13059)
  CONTRACT_NOT_FOUND: new ErrorCode(
    13040,
    'Không tìm thấy hợp đồng',
    HttpStatus.NOT_FOUND,
  ),
  CONTRACT_CODE_EXISTS: new ErrorCode(
    13041,
    'Mã hợp đồng đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),
  CONTRACT_CREATE_FAILED: new ErrorCode(
    13042,
    'Tạo hợp đồng thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  CONTRACT_UPDATE_FAILED: new ErrorCode(
    13043,
    'Cập nhật hợp đồng thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  CONTRACT_DELETE_FAILED: new ErrorCode(
    13044,
    'Xóa hợp đồng thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  CONTRACT_FIND_FAILED: new ErrorCode(
    13045,
    'Tìm kiếm hợp đồng thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  CONTRACT_ALREADY_TERMINATED: new ErrorCode(
    13046,
    'Hợp đồng đã bị chấm dứt',
    HttpStatus.BAD_REQUEST,
  ),
  CONTRACT_CANNOT_UPDATE_TERMINATED: new ErrorCode(
    13047,
    'Không thể cập nhật hợp đồng đã chấm dứt',
    HttpStatus.BAD_REQUEST,
  ),
  CONTRACT_INVALID_STATUS_CHANGE: new ErrorCode(
    13048,
    'Thay đổi trạng thái hợp đồng không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
};