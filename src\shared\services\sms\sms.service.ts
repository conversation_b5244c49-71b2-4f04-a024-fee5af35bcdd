import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SmsProviderFactory, SmsProviderType } from './sms-provider-factory.service';
import { SmsResponse, BulkSmsResponse, MessageStatusResponse, ConnectionTestResponse } from './sms-provider.interface';

/**
 * Service principal pour l'envoi de SMS
 */
@Injectable()
export class SmsService {
  private readonly logger = new Logger(SmsService.name);
  private readonly defaultProviderType: SmsProviderType;

  constructor(
    private readonly smsProviderFactory: SmsProviderFactory,
    private readonly configService: ConfigService
  ) {
    // Charger le type de fournisseur par défaut depuis la configuration
    const defaultProvider = this.configService.get<string>('SMS_DEFAULT_PROVIDER') || 'SPEED_SMS';
    this.defaultProviderType = SmsProviderType[defaultProvider] || SmsProviderType.SPEED_SMS;
    
    this.logger.log(`Fournisseur SMS par défaut: ${this.defaultProviderType}`);
  }

  /**
   * Envoie un SMS à un numéro de téléphone
   * @param phoneNumber Numéro de téléphone du destinataire
   * @param message Contenu du message
   * @param options Options supplémentaires
   * @returns Promesse contenant l'ID du message ou une erreur
   */
  async sendSms(
    phoneNumber: string, 
    message: string, 
    options?: { 
      providerType?: SmsProviderType;
      [key: string]: any;
    }
  ): Promise<SmsResponse> {
    const providerType = options?.providerType || this.defaultProviderType;
    
    try {
      this.logger.debug(`Envoi d'un SMS à ${phoneNumber} via ${providerType}`);
      
      const provider = this.smsProviderFactory.createProvider(providerType);
      
      // Supprimer providerType des options pour éviter les conflits
      const { providerType: _, ...providerOptions } = options || {};
      
      return await provider.sendSms(phoneNumber, message, providerOptions);
    } catch (error) {
      this.logger.error(`Erreur lors de l'envoi du SMS à ${phoneNumber}: ${error.message}`, error.stack);
      return {
        success: false,
        errorMessage: error.message || 'Erreur inconnue'
      };
    }
  }

  /**
   * Envoie un SMS à plusieurs numéros de téléphone
   * @param phoneNumbers Liste des numéros de téléphone des destinataires
   * @param message Contenu du message
   * @param options Options supplémentaires
   * @returns Promesse contenant les résultats pour chaque destinataire
   */
  async sendBulkSms(
    phoneNumbers: string[], 
    message: string, 
    options?: { 
      providerType?: SmsProviderType;
      [key: string]: any;
    }
  ): Promise<BulkSmsResponse> {
    const providerType = options?.providerType || this.defaultProviderType;
    
    try {
      this.logger.debug(`Envoi de SMS en masse à ${phoneNumbers.length} destinataires via ${providerType}`);
      
      const provider = this.smsProviderFactory.createProvider(providerType);
      
      // Supprimer providerType des options pour éviter les conflits
      const { providerType: _, ...providerOptions } = options || {};
      
      return await provider.sendBulkSms(phoneNumbers, message, providerOptions);
    } catch (error) {
      this.logger.error(`Erreur lors de l'envoi des SMS en masse: ${error.message}`, error.stack);
      
      // En cas d'exception, tous les messages sont considérés comme échoués
      const results = phoneNumbers.map(phone => ({
        phoneNumber: phone,
        success: false,
        errorMessage: error.message || 'Erreur inconnue'
      }));
      
      return {
        successCount: 0,
        failureCount: phoneNumbers.length,
        results
      };
    }
  }

  /**
   * Vérifie le statut d'un message envoyé
   * @param messageId ID du message à vérifier
   * @param providerType Type de fournisseur SMS
   * @returns Promesse contenant le statut du message
   */
  async checkMessageStatus(
    messageId: string, 
    providerType: SmsProviderType = this.defaultProviderType
  ): Promise<MessageStatusResponse> {
    try {
      this.logger.debug(`Vérification du statut du message ${messageId} via ${providerType}`);
      
      const provider = this.smsProviderFactory.createProvider(providerType);
      
      return await provider.checkMessageStatus(messageId);
    } catch (error) {
      this.logger.error(`Erreur lors de la vérification du statut du message ${messageId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Envoie un SMS avec un brandname
   * @param phoneNumber Numéro de téléphone du destinataire
   * @param message Contenu du message
   * @param brandname Nom de la marque à utiliser comme expéditeur
   * @param options Options supplémentaires
   * @returns Promesse contenant l'ID du message ou une erreur
   */
  async sendBrandnameSms(
    phoneNumber: string, 
    message: string, 
    brandname: string, 
    options?: { 
      providerType?: SmsProviderType;
      [key: string]: any;
    }
  ): Promise<SmsResponse> {
    const providerType = options?.providerType || this.defaultProviderType;
    
    try {
      this.logger.debug(`Envoi d'un SMS brandname à ${phoneNumber} via ${providerType}`);
      
      const provider = this.smsProviderFactory.createProvider(providerType);
      
      // Supprimer providerType des options pour éviter les conflits
      const { providerType: _, ...providerOptions } = options || {};
      
      return await provider.sendBrandnameSms(phoneNumber, message, brandname, providerOptions);
    } catch (error) {
      this.logger.error(`Erreur lors de l'envoi du SMS brandname à ${phoneNumber}: ${error.message}`, error.stack);
      return {
        success: false,
        errorMessage: error.message || 'Erreur inconnue'
      };
    }
  }

  /**
   * Envoie un SMS OTP (One-Time Password)
   * @param phoneNumber Numéro de téléphone du destinataire
   * @param otpCode Code OTP à envoyer
   * @param options Options supplémentaires
   * @returns Promesse contenant l'ID du message ou une erreur
   */
  async sendOtp(
    phoneNumber: string, 
    otpCode: string, 
    options?: { 
      providerType?: SmsProviderType;
      template?: string;
      [key: string]: any;
    }
  ): Promise<SmsResponse> {
    const providerType = options?.providerType || this.defaultProviderType;
    
    try {
      this.logger.debug(`Envoi d'un code OTP à ${phoneNumber} via ${providerType}`);
      
      const provider = this.smsProviderFactory.createProvider(providerType);
      
      // Supprimer providerType des options pour éviter les conflits
      const { providerType: _, ...providerOptions } = options || {};
      
      return await provider.sendOtp(phoneNumber, otpCode, providerOptions);
    } catch (error) {
      this.logger.error(`Erreur lors de l'envoi du code OTP à ${phoneNumber}: ${error.message}`, error.stack);
      return {
        success: false,
        errorMessage: error.message || 'Erreur inconnue'
      };
    }
  }

  /**
   * Teste la connexion avec un fournisseur SMS
   * @param providerType Type de fournisseur SMS
   * @param config Configuration du fournisseur
   * @returns Promesse indiquant si la connexion est réussie
   */
  async testConnection(
    providerType: SmsProviderType = this.defaultProviderType,
    config: any = {}
  ): Promise<ConnectionTestResponse> {
    try {
      this.logger.debug(`Test de connexion avec ${providerType}`);
      
      const provider = this.smsProviderFactory.createProvider(providerType);
      
      return await provider.testConnection(config);
    } catch (error) {
      this.logger.error(`Erreur lors du test de connexion avec ${providerType}: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Erreur inconnue'
      };
    }
  }
}
