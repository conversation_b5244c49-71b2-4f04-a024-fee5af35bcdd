import { Injectable, Logger } from '@nestjs/common';
import { AdminTemplateEmailService } from '../services/admin-template-email.service';
import { TemplateEmailService } from '@shared/services/template-email';
import { AppException } from '@/common/exceptions/app.exception';
import { ErrorCode } from '@/common/exceptions/app.exception';
import {
  WelcomeEmailPlaceholder,
  OrderConfirmationEmailPlaceholder,
  PaymentSuccessEmailPlaceholder,
  OrderStatusUpdateEmailPlaceholder,
  VerifyOtpEmailPlaceholder,
  ForgotPasswordEmailPlaceholder,
  TwoFactorAuthEmailPlaceholder,
  EmailTemplateCategory
} from './email-placeholder.enum';

/**
 * Service xử lý gửi email cho các module khác
 */
@Injectable()
export class EmailSenderService {
  private readonly logger = new Logger(EmailSenderService.name);

  constructor(
    private readonly adminTemplateEmailService: AdminTemplateEmailService,
    private readonly templateEmailService: TemplateEmailService,
  ) {}

  /**
   * Gửi email chào mừng cho người dùng mới
   * @param email Email người dùng
   * @param name Tên người dùng
   * @returns true nếu gửi thành công
   */
  async sendWelcomeEmail(email: string, name: string): Promise<boolean> {
    try {
      // Lấy template từ database
      const template = await this.adminTemplateEmailService.findByCategory(EmailTemplateCategory.USER_WELCOME);

      // Chuẩn bị dữ liệu cho các placeholder
      const placeholderData = {
        [WelcomeEmailPlaceholder.NAME]: name,
        [WelcomeEmailPlaceholder.DATE]: new Date().toLocaleDateString('vi-VN'),
      };

      // Gửi email sử dụng shared service
      return await this.templateEmailService.sendTemplateEmail({
        template: template.content,
        subject: template.subject,
        to: email,
        data: placeholderData,
      });
    } catch (error) {
      this.logger.error(`Error sending welcome email: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EMAIL_SENDING_ERROR,
        'Không thể gửi email chào mừng',
      );
    }
  }

  /**
   * Gửi email thông báo đơn hàng mới
   * @param email Email người dùng
   * @param name Tên người dùng
   * @param orderId Mã đơn hàng
   * @param orderItems Danh sách sản phẩm
   * @param totalAmount Tổng tiền
   * @returns true nếu gửi thành công
   */
  async sendNewOrderEmail(
    email: string,
    name: string,
    orderId: string,
    orderItems: Array<{ name: string; quantity: number; price: number }>,
    totalAmount: number,
  ): Promise<boolean> {
    try {
      // Tạo HTML cho danh sách sản phẩm
      const itemsHtml = orderItems
        .map(
          (item) =>
            `<tr>
              <td>${item.name}</td>
              <td>${item.quantity}</td>
              <td>${item.price.toLocaleString('vi-VN')} đ</td>
              <td>${(item.quantity * item.price).toLocaleString('vi-VN')} đ</td>
            </tr>`,
        )
        .join('');

      // Lấy template từ database
      const template = await this.adminTemplateEmailService.findByCategory(EmailTemplateCategory.ORDER_CONFIRMATION);

      // Chuẩn bị dữ liệu cho các placeholder
      const placeholderData = {
        [OrderConfirmationEmailPlaceholder.NAME]: name,
        [OrderConfirmationEmailPlaceholder.ORDER_ID]: orderId,
        [OrderConfirmationEmailPlaceholder.ORDER_DATE]: new Date().toLocaleDateString('vi-VN'),
        [OrderConfirmationEmailPlaceholder.ITEMS]: itemsHtml,
        [OrderConfirmationEmailPlaceholder.TOTAL_AMOUNT]: totalAmount.toLocaleString('vi-VN'),
      };

      // Gửi email sử dụng shared service
      return await this.templateEmailService.sendTemplateEmail({
        template: template.content,
        subject: template.subject,
        to: email,
        data: placeholderData,
      });
    } catch (error) {
      this.logger.error(`Error sending order confirmation email: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EMAIL_SENDING_ERROR,
        'Không thể gửi email xác nhận đơn hàng',
      );
    }
  }

  /**
   * Gửi email thông báo thanh toán thành công
   * @param email Email người dùng
   * @param name Tên người dùng
   * @param orderId Mã đơn hàng
   * @param amount Số tiền đã thanh toán
   * @param paymentMethod Phương thức thanh toán
   * @returns true nếu gửi thành công
   */
  async sendPaymentSuccessEmail(
    email: string,
    name: string,
    orderId: string,
    amount: number,
    paymentMethod: string,
  ): Promise<boolean> {
    try {
      // Lấy template từ database
      const template = await this.adminTemplateEmailService.findByCategory(EmailTemplateCategory.PAYMENT_SUCCESS);

      // Chuẩn bị dữ liệu cho các placeholder
      const placeholderData = {
        [PaymentSuccessEmailPlaceholder.NAME]: name,
        [PaymentSuccessEmailPlaceholder.ORDER_ID]: orderId,
        [PaymentSuccessEmailPlaceholder.AMOUNT]: amount.toLocaleString('vi-VN'),
        [PaymentSuccessEmailPlaceholder.PAYMENT_METHOD]: paymentMethod,
        [PaymentSuccessEmailPlaceholder.PAYMENT_DATE]: new Date().toLocaleDateString('vi-VN'),
      };

      // Gửi email sử dụng shared service
      return await this.templateEmailService.sendTemplateEmail({
        template: template.content,
        subject: template.subject,
        to: email,
        data: placeholderData,
      });
    } catch (error) {
      this.logger.error(`Error sending payment success email: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EMAIL_SENDING_ERROR,
        'Không thể gửi email xác nhận thanh toán',
      );
    }
  }

  /**
   * Gửi email thông báo cập nhật trạng thái đơn hàng
   * @param email Email người dùng
   * @param name Tên người dùng
   * @param orderId Mã đơn hàng
   * @param status Trạng thái mới
   * @param statusDescription Mô tả trạng thái
   * @returns true nếu gửi thành công
   */
  async sendOrderStatusUpdateEmail(
    email: string,
    name: string,
    orderId: string,
    status: string,
    statusDescription: string,
  ): Promise<boolean> {
    try {
      // Lấy template từ database
      const template = await this.adminTemplateEmailService.findByCategory(EmailTemplateCategory.ORDER_STATUS_UPDATE);

      // Chuẩn bị dữ liệu cho các placeholder
      const placeholderData = {
        [OrderStatusUpdateEmailPlaceholder.NAME]: name,
        [OrderStatusUpdateEmailPlaceholder.ORDER_ID]: orderId,
        [OrderStatusUpdateEmailPlaceholder.STATUS]: status,
        [OrderStatusUpdateEmailPlaceholder.STATUS_DESCRIPTION]: statusDescription,
        [OrderStatusUpdateEmailPlaceholder.UPDATE_DATE]: new Date().toLocaleDateString('vi-VN'),
      };

      // Gửi email sử dụng shared service
      return await this.templateEmailService.sendTemplateEmail({
        template: template.content,
        subject: template.subject,
        to: email,
        data: placeholderData,
      });
    } catch (error) {
      this.logger.error(`Error sending order status update email: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EMAIL_SENDING_ERROR,
        'Không thể gửi email cập nhật trạng thái đơn hàng',
      );
    }
  }

  /**
   * Kiểm tra xem dữ liệu có chứa đầy đủ các placeholder cần thiết không
   * @param data Dữ liệu cần kiểm tra
   * @param requiredPlaceholders Danh sách các placeholder bắt buộc
   * @returns true nếu dữ liệu hợp lệ, false nếu thiếu placeholder
   */
  private validatePlaceholders(data: Record<string, any>, requiredPlaceholders: string[]): boolean {
    for (const placeholder of requiredPlaceholders) {
      if (data[placeholder] === undefined) {
        return false;
      }
    }
    return true;
  }

  /**
   * Gửi email với template tùy chỉnh
   * @param category Danh mục template
   * @param to Email người nhận
   * @param data Dữ liệu thay thế placeholder
   * @returns true nếu gửi thành công
   */
  async sendTemplateEmail(category: string, to: string, data: Record<string, any>): Promise<boolean> {
    try {
      // Lấy template từ database
      const template = await this.adminTemplateEmailService.findByCategory(category);

      // Kiểm tra xem template có tồn tại không
      if (!template) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy template email với danh mục ${category}`,
        );
      }

      // Kiểm tra xem template có định nghĩa placeholders không
      if (template.placeholders) {
        // Lấy danh sách các placeholder bắt buộc
        const requiredPlaceholders = Object.keys(template.placeholders);

        // Kiểm tra xem dữ liệu có chứa đầy đủ các placeholder không
        if (!this.validatePlaceholders(data, requiredPlaceholders)) {
          throw new AppException(
            ErrorCode.VALIDATION_ERROR,
            `Dữ liệu không chứa đầy đủ các placeholder cần thiết: ${requiredPlaceholders.join(', ')}`,
          );
        }
      }

      // Gửi email sử dụng shared service
      return await this.templateEmailService.sendTemplateEmail({
        template: template.content,
        subject: template.subject,
        to,
        data,
      });
    } catch (error) {
      this.logger.error(`Error sending template email: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EMAIL_SENDING_ERROR,
        'Không thể gửi email',
      );
    }
  }

  /**
   * Gửi email xác thực OTP
   * @param email Email người nhận
   * @param name Tên người nhận
   * @param otpCode Mã OTP
   * @param expireTime Thời gian hết hạn (phút)
   * @param appName Tên ứng dụng
   * @returns true nếu gửi thành công
   */
  async sendVerifyOtpEmail(
    email: string,
    name: string,
    otpCode: string,
    expireTime: number = 5,
    appName: string = 'RedAI',
  ): Promise<boolean> {
    try {
      // Lấy template từ database
      const template = await this.adminTemplateEmailService.findByCategory(EmailTemplateCategory.VERIFY_OTP);

      // Chuẩn bị dữ liệu cho các placeholder
      const placeholderData = {
        [VerifyOtpEmailPlaceholder.NAME]: name,
        [VerifyOtpEmailPlaceholder.OTP_CODE]: otpCode,
        [VerifyOtpEmailPlaceholder.EXPIRE_TIME]: expireTime.toString(),
        [VerifyOtpEmailPlaceholder.APP_NAME]: appName,
      };

      // Gửi email sử dụng shared service
      return await this.templateEmailService.sendTemplateEmail({
        template: template.content,
        subject: template.subject,
        to: email,
        data: placeholderData,
      });
    } catch (error) {
      this.logger.error(`Error sending verify OTP email: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EMAIL_SENDING_ERROR,
        'Không thể gửi email xác thực OTP',
      );
    }
  }

  /**
   * Gửi email quên mật khẩu
   * @param email Email người nhận
   * @param name Tên người nhận
   * @param resetLink Đường dẫn đặt lại mật khẩu
   * @param expireTime Thời gian hết hạn (phút)
   * @param appName Tên ứng dụng
   * @returns true nếu gửi thành công
   */
  async sendForgotPasswordEmail(
    email: string,
    name: string,
    resetLink: string,
    expireTime: number = 60,
    appName: string = 'RedAI',
  ): Promise<boolean> {
    try {
      // Lấy template từ database
      const template = await this.adminTemplateEmailService.findByCategory(EmailTemplateCategory.FORGOT_PASSWORD);

      // Chuẩn bị dữ liệu cho các placeholder
      const placeholderData = {
        [ForgotPasswordEmailPlaceholder.NAME]: name,
        [ForgotPasswordEmailPlaceholder.RESET_LINK]: resetLink,
        [ForgotPasswordEmailPlaceholder.EXPIRE_TIME]: expireTime.toString(),
        [ForgotPasswordEmailPlaceholder.APP_NAME]: appName,
      };

      // Gửi email sử dụng shared service
      return await this.templateEmailService.sendTemplateEmail({
        template: template.content,
        subject: template.subject,
        to: email,
        data: placeholderData,
      });
    } catch (error) {
      this.logger.error(`Error sending forgot password email: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EMAIL_SENDING_ERROR,
        'Không thể gửi email quên mật khẩu',
      );
    }
  }

  /**
   * Gửi email xác thực 2 lớp
   * @param email Email người nhận
   * @param name Tên người nhận
   * @param authCode Mã xác thực
   * @param deviceInfo Thông tin thiết bị
   * @param ipAddress Địa chỉ IP
   * @param location Vị trí
   * @param expireTime Thời gian hết hạn (phút)
   * @returns true nếu gửi thành công
   */
  async sendTwoFactorAuthEmail(
    email: string,
    name: string,
    authCode: string,
    deviceInfo: string,
    ipAddress: string,
    location: string,
    expireTime: number = 5,
  ): Promise<boolean> {
    try {
      // Lấy template từ database
      const template = await this.adminTemplateEmailService.findByCategory(EmailTemplateCategory.TWO_FACTOR_AUTH);

      // Chuẩn bị dữ liệu cho các placeholder
      const placeholderData = {
        [TwoFactorAuthEmailPlaceholder.NAME]: name,
        [TwoFactorAuthEmailPlaceholder.AUTH_CODE]: authCode,
        [TwoFactorAuthEmailPlaceholder.EXPIRE_TIME]: expireTime.toString(),
        [TwoFactorAuthEmailPlaceholder.DEVICE_INFO]: deviceInfo,
        [TwoFactorAuthEmailPlaceholder.IP_ADDRESS]: ipAddress,
        [TwoFactorAuthEmailPlaceholder.LOCATION]: location,
        [TwoFactorAuthEmailPlaceholder.TIME]: new Date().toLocaleString('vi-VN'),
      };

      // Gửi email sử dụng shared service
      return await this.templateEmailService.sendTemplateEmail({
        template: template.content,
        subject: template.subject,
        to: email,
        data: placeholderData,
      });
    } catch (error) {
      this.logger.error(`Error sending two-factor auth email: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EMAIL_SENDING_ERROR,
        'Không thể gửi email xác thực 2 lớp',
      );
    }
  }
}
