import { Test, TestingModule } from '@nestjs/testing';
import { DataSource } from 'typeorm';
import { TypeOrmModule } from '@nestjs/typeorm';

describe('DatabaseModule', () => {
  let module: TestingModule;
  let dataSource: DataSource;
  // Mock database config
  const mockDatabaseConfig = {
    host: 'localhost',
    port: 5432,
    username: 'test_user',
    password: 'test_password',
    database: 'test_db',
    ssl: false,
  };

  beforeEach(async () => {
    // Create a testing module
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'postgres',
          host: mockDatabaseConfig.host,
          port: mockDatabaseConfig.port,
          username: mockDatabaseConfig.username,
          password: mockDatabaseConfig.password,
          database: mockDatabaseConfig.database,
          autoLoadEntities: true,
          synchronize: false,
          ssl: {
            rejectUnauthorized: !mockDatabaseConfig.ssl,
          },
          // Use in-memory database for testing
        }),
      ],
    }).compile();

    // Get the DataSource instance
    dataSource = module.get<DataSource>(DataSource);
  });

  afterEach(async () => {
    // Close the connection after each test
    if (dataSource && dataSource.isInitialized) {
      await dataSource.destroy();
    }
    await module.close();
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
    expect(dataSource).toBeDefined();
  });

  it('should use the correct database configuration', () => {
    // Verify that the DataSource was configured correctly
    expect(dataSource.options).toMatchObject({
      type: 'postgres',
      host: mockDatabaseConfig.host,
      port: mockDatabaseConfig.port,
      username: mockDatabaseConfig.username,
      password: mockDatabaseConfig.password,
      database: mockDatabaseConfig.database,
      ssl: {
        rejectUnauthorized: !mockDatabaseConfig.ssl,
      },
    });
  });

  it('should have entities configured correctly', () => {
    // Check if entities are configured
    expect(dataSource.options).toBeDefined();

    // TypeORM DataSource options structure might vary, so we need to check carefully
    const options = dataSource.options as any;

    // Check if autoLoadEntities is set (if available in your TypeORM version)
    if ('autoLoadEntities' in options) {
      expect(options.autoLoadEntities).toBe(true);
    }

    // Check if synchronize is set (if available in your TypeORM version)
    if ('synchronize' in options) {
      expect(options.synchronize).toBe(false);
    }
  });
});
