import { EventEmitter } from 'events';
import { JwtPayload } from '../../interfaces/jwt-payload.interface';

/**
 * Mock cho Socket.IO client
 */
export class SocketMock extends EventEmitter {
  id: string;
  handshake: any;
  rooms: Set<string>;
  user?: JwtPayload;
  connectedAt?: Date;
  lastActiveAt?: Date;
  status?: 'online' | 'away' | 'busy' | 'offline';
  device?: {
    type?: string;
    browser?: string;
    os?: string;
    version?: string;
  };

  constructor(id: string = 'socket-id') {
    super();
    this.id = id;
    this.handshake = {
      auth: {},
      headers: {},
      query: {},
    };
    this.rooms = new Set<string>();
  }

  /**
   * Mock cho phương thức join
   * @param room Tên phòng
   * @returns Promise<void>
   */
  async join(room: string): Promise<void> {
    this.rooms.add(room);
    return Promise.resolve();
  }

  /**
   * <PERSON>ck cho phương thức leave
   * @param room Tên phòng
   * @returns Promise<void>
   */
  async leave(room: string): Promise<void> {
    this.rooms.delete(room);
    return Promise.resolve();
  }

  /**
   * <PERSON>ck cho phương thức to
   * @param room Tên phòng
   * @returns this
   */
  to(room: string): any {
    return {
      emit: jest.fn(),
      except: (socketIds: string[]) => ({
        emit: jest.fn(),
      }),
    };
  }

  /**
   * Mock cho phương thức emit
   * @param event Tên sự kiện
   * @param data Dữ liệu
   * @returns boolean
   */
  emit(event: string, data: any): boolean {
    return true;
  }

  /**
   * Thiết lập token xác thực
   * @param token Token JWT
   */
  setAuthToken(token: string): void {
    this.handshake.auth.token = token;
  }

  /**
   * Thiết lập header xác thực
   * @param token Token JWT
   */
  setAuthHeader(token: string): void {
    this.handshake.headers.authorization = `Bearer ${token}`;
  }

  /**
   * Thiết lập query xác thực
   * @param token Token JWT
   */
  setAuthQuery(token: string): void {
    this.handshake.query.token = token;
  }

  /**
   * Thiết lập thông tin người dùng
   * @param user Thông tin người dùng
   */
  setUser(user: JwtPayload): void {
    this.user = user;
  }
}
