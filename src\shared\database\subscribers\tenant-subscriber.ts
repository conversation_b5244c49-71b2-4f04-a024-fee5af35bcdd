import { Injectable, Logger } from '@nestjs/common';
import {
  DataSource,
  EntitySubscriberInterface,
  InsertEvent,
  UpdateEvent,
  RemoveEvent,
  EventSubscriber,
  BeforeQueryEvent,
} from 'typeorm';

/**
 * Interface cho các entity có tenantId
 */
export interface TenantEntity {
  tenantId: number | null;
}

/**
 * Subscriber để tự động áp dụng điều kiện tenantId cho tất cả các truy vấn
 * Đảm bảo rằng người dùng chỉ có thể truy cập dữ liệu thuộc về công ty của họ
 */
@Injectable()
@EventSubscriber()
export class TenantSubscriber implements EntitySubscriberInterface {
  private readonly logger = new Logger(TenantSubscriber.name);

  constructor(dataSource: DataSource) {
    dataSource.subscribers.push(this);
  }

  /**
   * Kiểm tra xem entity có trường tenantId không
   * @param entity Entity cần kiểm tra
   * @returns true nếu entity có trường tenantId, false nếu không
   */
  private hasTenantId(entity: any): entity is TenantEntity {
    return entity && 'tenantId' in entity;
  }

  /**
   * Xử lý trước khi thêm mới entity
   * @param event Sự kiện thêm mới
   */
  beforeInsert(_event: InsertEvent<any>): void {
    // Trong thực tế, tenantId sẽ được thêm vào từ controller hoặc service
    // Subscriber này chỉ kiểm tra và đảm bảo rằng tenantId không bị thay đổi
    this.logger.debug('Kiểm tra tenantId trước khi thêm mới entity');
  }

  /**
   * Xử lý trước khi cập nhật entity
   * @param event Sự kiện cập nhật
   */
  beforeUpdate(event: UpdateEvent<any>): void {
    // Không cho phép thay đổi tenantId
    if (
      event.entity &&
      this.hasTenantId(event.entity) &&
      event.databaseEntity &&
      this.hasTenantId(event.databaseEntity) &&
      event.entity.tenantId !== event.databaseEntity.tenantId
    ) {
      this.logger.warn('Cố gắng thay đổi tenantId của entity');
      event.entity.tenantId = event.databaseEntity.tenantId;
    }
  }

  /**
   * Xử lý trước khi xóa entity
   * @param event Sự kiện xóa
   */
  beforeRemove(_event: RemoveEvent<any>): void {
    // Trong thực tế, việc kiểm tra quyền xóa sẽ được thực hiện ở controller hoặc service
    this.logger.debug('Kiểm tra tenantId trước khi xóa entity');
  }

  /**
   * Xử lý trước khi thực hiện truy vấn
   * @param event Sự kiện truy vấn
   */
  beforeQuery(_event: BeforeQueryEvent<any>): Promise<any> | void {
    // Trong thực tế, việc lọc theo tenantId sẽ được thực hiện ở repository
    this.logger.debug('Kiểm tra tenantId trước khi thực hiện truy vấn');
  }
}
