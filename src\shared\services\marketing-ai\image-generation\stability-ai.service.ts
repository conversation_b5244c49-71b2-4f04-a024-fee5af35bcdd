import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AxiosRequestConfig } from 'axios';
import { BaseMarketingAiServiceImpl } from '../base-marketing-ai.service';
import {
  ImageGenerationOptions,
  ImageGenerationResult,
  ImageGenerationService,
  ImageSize,
  ImageStyle,
  MarketingAiResponse,
} from '../interfaces';

/**
 * Stability AI service for image generation
 */
@Injectable()
export class StabilityAiService extends BaseMarketingAiServiceImpl implements ImageGenerationService {
  readonly serviceName = 'Stability AI';
  protected readonly baseUrl = 'https://api.stability.ai/v1';
  protected readonly apiKey: string | undefined;

  constructor(private readonly configService: ConfigService) {
    super(StabilityAiService.name);
    this.apiKey = this.configService.get<string>('STABILITY_API_KEY');

    if (!this.apiKey) {
      this.logger.warn('STABILITY_API_KEY is not defined in environment variables');
    }
  }

  /**
   * Test the connection to the Stability AI API
   * @returns A promise that resolves to a boolean indicating if the connection was successful
   */
  async testConnection(): Promise<boolean> {
    try {
      const url = `${this.baseUrl}/engines/list`;
      const config = this.createRequestConfig();

      const response = await this.axiosInstance.get(url, config);
      return response.status === 200;
    } catch (error) {
      this.logger.error(`Connection test failed: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Create a request configuration with authentication
   * @param config Additional request configuration
   * @returns Request configuration with authentication
   */
  protected createRequestConfig(config?: AxiosRequestConfig): AxiosRequestConfig {
    if (!this.apiKey) {
      throw new Error('Stability AI API key is not defined');
    }

    return {
      ...config,
      headers: {
        ...config?.headers,
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    };
  }

  /**
   * Map size option to Stability AI width and height
   * @param size Size option
   * @param width Custom width (if size is CUSTOM)
   * @param height Custom height (if size is CUSTOM)
   * @returns Object with width and height
   */
  private mapSizeToStabilitySize(
    size: ImageSize = ImageSize.MEDIUM,
    width?: number,
    height?: number,
  ): { width: number; height: number } {
    if (size === ImageSize.CUSTOM && width && height) {
      // Stability AI requires dimensions to be multiples of 64
      const adjustedWidth = Math.round(width / 64) * 64;
      const adjustedHeight = Math.round(height / 64) * 64;

      return {
        width: Math.min(Math.max(adjustedWidth, 512), 1536),
        height: Math.min(Math.max(adjustedHeight, 512), 1536),
      };
    }

    switch (size) {
      case ImageSize.SMALL:
        return { width: 512, height: 512 };
      case ImageSize.MEDIUM:
        return { width: 768, height: 768 };
      case ImageSize.LARGE:
        return { width: 1024, height: 1024 };
      default:
        return { width: 768, height: 768 };
    }
  }

  /**
   * Map style option to Stability AI style preset
   * @param style Style option
   * @returns Stability AI style preset
   */
  private mapStyleToStabilityStyle(style?: ImageStyle): string {
    switch (style) {
      case ImageStyle.REALISTIC:
        return 'photographic';
      case ImageStyle.ARTISTIC:
        return 'digital-art';
      case ImageStyle.CARTOON:
        return 'comic-book';
      case ImageStyle.SKETCH:
        return 'line-art';
      case ImageStyle.ABSTRACT:
        return 'abstract';
      case ImageStyle.PHOTOGRAPHIC:
        return 'photographic';
      default:
        return 'photographic';
    }
  }

  /**
   * Generate images from a text prompt using Stability AI
   * @param prompt Text prompt to generate images from
   * @param options Options for image generation
   * @returns A promise that resolves to a response containing the generated images
   */
  async generateImage(
    prompt: string,
    options?: ImageGenerationOptions,
  ): Promise<MarketingAiResponse<ImageGenerationResult>> {
    try {
      const { width, height } = this.mapSizeToStabilitySize(
        options?.size,
        options?.width,
        options?.height,
      );

      const count = options?.count || 1;
      const stylePreset = this.mapStyleToStabilityStyle(options?.style);

      // Default to stable-diffusion-xl-1024-v1-0 engine
      const engineId = 'stable-diffusion-xl-1024-v1-0';
      const url = `${this.baseUrl}/generation/${engineId}/text-to-image`;

      const requestData = {
        text_prompts: [
          {
            text: prompt,
            weight: 1,
          },
        ],
        cfg_scale: 7,
        height,
        width,
        samples: count,
        steps: 30,
        style_preset: stylePreset,
      };

      // Add negative prompt if provided
      if (options?.negativePrompt) {
        requestData.text_prompts.push({
          text: options.negativePrompt,
          weight: -1,
        });
      }

      // Add seed if provided
      if (options?.seed) {
        requestData['seed'] = options.seed;
      }

      const config = this.createRequestConfig({
        timeout: options?.timeout || 60000,
      });

      const response = await this.axiosInstance.post(url, requestData, config);

      // Extract image URLs and seeds
      const imageUrls = response.data.artifacts.map(
        (artifact: any) => `data:image/png;base64,${artifact.base64}`,
      );

      const seeds = response.data.artifacts.map(
        (artifact: any) => artifact.seed,
      );

      // Create result
      const result: ImageGenerationResult = {
        imageUrls,
        seeds,
        prompt,
      };

      return this.createSuccessResponse(result, response.data);
    } catch (error) {
      return this.handleApiError<ImageGenerationResult>(error, 'Stability AI image generation');
    }
  }

  /**
   * Edit an existing image using a text prompt
   * @param imageUrl URL of the image to edit
   * @param prompt Text prompt to guide the editing
   * @param options Options for image editing
   * @returns A promise that resolves to a response containing the edited image
   */
  async editImage(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    imageUrl: string,
    prompt: string,
    options?: ImageGenerationOptions,
  ): Promise<MarketingAiResponse<ImageGenerationResult>> {
    try {
      // Stability AI supports image-to-image generation
      // We need to download the image, convert it to base64, and send it to the API

      // For simplicity, we'll just log a warning and generate a new image
      this.logger.warn('Image editing with Stability AI requires downloading and processing the image. Generating a new image based on the prompt.');

      return this.generateImage(
        `Based on the reference image: ${prompt}`,
        options,
      );
    } catch (error) {
      return this.handleApiError<ImageGenerationResult>(error, 'Stability AI image editing');
    }
  }

  /**
   * Generate image variations from an existing image
   * @param imageUrl URL of the image to create variations from
   * @param options Options for image variation generation
   * @returns A promise that resolves to a response containing the image variations
   */
  async generateImageVariations(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    imageUrl: string,
    options?: ImageGenerationOptions,
  ): Promise<MarketingAiResponse<ImageGenerationResult>> {
    try {
      // Stability AI supports image-to-image generation
      // We need to download the image, convert it to base64, and send it to the API

      // For simplicity, we'll just log a warning and generate a new image
      this.logger.warn('Image variations with Stability AI requires downloading and processing the image. Generating a new image with a generic prompt.');

      return this.generateImage(
        'Create a variation of the reference image',
        options,
      );
    } catch (error) {
      return this.handleApiError<ImageGenerationResult>(error, 'Stability AI image variations');
    }
  }
}
