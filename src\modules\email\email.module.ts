import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdminTemplateEmail } from './entities/admin-template-email.entity';
import { AdminTemplateEmailRepository } from './repositories/admin-template-email.repository';
import { AdminTemplateEmailService } from './services/admin-template-email.service';
import { UserEmailService } from './services/user-email.service';
import { AdminTemplateEmailController } from './controllers/admin-template-email.controller';
import { EmailModule as SharedEmailModule } from '@shared/services/email';
import { TemplateEmailModule } from '@shared/services/template-email';
import { EmailSenderService } from './shared';

@Module({
  imports: [
    TypeOrmModule.forFeature([AdminTemplateEmail]),
    SharedEmailModule,
    TemplateEmailModule,
  ],
  controllers: [
    AdminTemplateEmailController,
  ],
  providers: [
    AdminTemplateEmailRepository,
    AdminTemplateEmailService,
    UserEmailService,
    EmailSenderService,
  ],
  exports: [
    AdminTemplateEmailService,
    UserEmailService,
    EmailSenderService,
  ],
})
export class EmailModule {}
