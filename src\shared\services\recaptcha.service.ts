import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { AxiosError } from 'axios';
import { AppException, ErrorCode } from '@/common';

interface RecaptchaResponse {
  success: boolean;
  challenge_ts: string; // Timestamp of the challenge load (ISO format yyyy-MM-dd'T'HH:mm:ssZZ)
  hostname: string;     // The hostname of the site where the reCAPTCHA was solved
  'error-codes'?: string[]; // Optional error codes
  score?: number;       // Score for v3
  action?: string;      // Action name for v3
}

@Injectable()
export class RecaptchaService {
  private readonly logger = new Logger(RecaptchaService.name);
  private readonly googleVerifyUrl = 'https://www.google.com/recaptcha/api/siteverify';
  private readonly secretKey: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    const key = this.configService.get<string>('RECAPTCHA_SECRET_KEY');
    if (!key) {
      this.logger.error('RECAPTCHA_SECRET_KEY is not defined in environment variables! Recaptcha verification will fail.');
      // Ném lỗi nếu bắt buộc
      // throw new Error('RECAPTCHA_SECRET_KEY is not defined');
      this.secretKey = ''; // Gán giá trị rỗng để tránh lỗi typescript nhưng service sẽ không hoạt động
    } else {
      this.secretKey = key;
    }
  }

  /**
   * Verifies the Google reCAPTCHA token.
   * @param token The reCAPTCHA token received from the client.
   * @param remoteIp Optional: The user's IP address.
   * @returns Promise resolving to the RecaptchaResponse from Google.
   * @throws {HttpException} If verification fails or secret key is missing.
   */
  async verifyRecaptcha(token: string, remoteIp?: string): Promise<RecaptchaResponse> {
    if (!this.secretKey) {
       this.logger.error('Attempted to verify reCAPTCHA without a secret key.');
       throw new AppException(ErrorCode.RECAPTCHA_CONFIG_ERROR);
    }

    const params = new URLSearchParams();
    params.append('secret', this.secretKey);
    params.append('response', token);
    if (remoteIp) {
      params.append('remoteip', remoteIp);
    }

    try {
      const observable = this.httpService.post<RecaptchaResponse>(
        this.googleVerifyUrl,
        params,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );
      const response = await firstValueFrom(observable);

      this.logger.debug('Google reCAPTCHA Response:', response.data);

      return response.data;

    } catch (error) {
        // Kiểm tra lỗi Axios cụ thể
        if (error instanceof AxiosError) {
            this.logger.error(`Error verifying reCAPTCHA: ${error.message}`, error.stack);
            const errorCodes = error.response?.data?.['error-codes'] as string[] | undefined;
            const errorMessage = errorCodes ? errorCodes.join(', ') : (error.response?.data?.message || error.message);
            throw new AppException(
                ErrorCode.RECAPTCHA_VERIFICATION_FAILED,
                `Failed to verify reCAPTCHA: ${errorMessage}`
            );
        } else {
            // Các lỗi khác không phải Axios
            this.logger.error(`Unexpected error verifying reCAPTCHA: ${error.message}`, error.stack);
            throw new AppException(
                ErrorCode.EXTERNAL_SERVICE_ERROR,
                'An unexpected error occurred during reCAPTCHA verification.'
            );
        }
    }
  }
}