import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { User } from '../entities/user.entity';
import { UserStatus } from '../enum/user-status.enum';

/**
 * Repository cho User
 */
@Injectable()
export class UserRepository {

  constructor(
    @InjectRepository(User)
    private readonly repository: Repository<User>,
  ) {}

  /**
   * Tìm người dùng theo email
   * @param email Email cần tìm
   * @returns Thông tin người dùng hoặc null nếu không tìm thấy
   */
  async findByEmail(email: string): Promise<User | null> {
    return this.repository.findOne({ where: { email } });
  }

  /**
   * Tìm người dùng theo username
   * @param username Username cần tìm
   * @returns Thông tin người dùng hoặc null nếu không tìm thấy
   */
  async findByUsername(username: string): Promise<User | null> {
    return this.repository.findOne({ where: { username } });
  }

  /**
   * Tìm người dùng theo ID
   * @param id ID cần tìm
   * @returns Thông tin người dùng hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<User | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm nhiều người dùng theo danh sách ID
   * @param ids Danh sách ID cần tìm
   * @returns Danh sách người dùng
   */
  async findByIds(ids: number[]): Promise<User[]> {
    return this.repository.find({
      where: { id: In(ids) }
    });
  }

  /**
   * Tìm tất cả người dùng thuộc một phòng ban
   * @param departmentId ID phòng ban
   * @returns Danh sách người dùng thuộc phòng ban
   */
  async findByDepartmentId(departmentId: number): Promise<User[]> {
    return this.repository.find({
      where: { departmentId },
      order: { fullName: 'ASC' },
    });
  }

  /**
   * Đếm số lượng người dùng theo phòng ban
   * @returns Danh sách kết quả đếm theo phòng ban
   */
  async countByDepartments(): Promise<{ departmentId: number; count: number }[]> {
    const result = await this.repository
      .createQueryBuilder('user')
      .select('user.departmentId', 'departmentId')
      .addSelect('COUNT(user.id)', 'count')
      .where('user.departmentId IS NOT NULL')
      .groupBy('user.departmentId')
      .getRawMany();

    return result.map(item => ({
      departmentId: parseInt(item.departmentId),
      count: parseInt(item.count)
    }));
  }

  /**
   * Tạo mới người dùng
   * @param data Dữ liệu người dùng
   * @returns Thông tin người dùng đã tạo
   */
  async create(data: Partial<User>): Promise<User> {
    const user = this.repository.create(data);
    return this.repository.save(user);
  }

  /**
   * Cập nhật thông tin người dùng
   * @param id ID người dùng cần cập nhật
   * @param data Dữ liệu cần cập nhật
   * @returns Thông tin người dùng đã cập nhật
   * @throws Error nếu không tìm thấy người dùng sau khi cập nhật
   */
  async update(id: number, data: Partial<User>): Promise<User> {
    await this.repository.update(id, data);
    const updatedUser = await this.findById(id);

    if (!updatedUser) {
      throw new Error(`User with ID ${id} not found after update`);
    }

    return updatedUser;
  }

  /**
   * Cập nhật trạng thái người dùng
   * @param id ID người dùng cần cập nhật
   * @param status Trạng thái mới
   * @returns Thông tin người dùng đã cập nhật
   */
  async updateStatus(id: number, status: UserStatus): Promise<User> {
    return this.update(id, { status });
  }
}
