import { ExecutionContext } from '@nestjs/common';
import { SocketRoom } from '../../decorators/socket-room.decorator';
import { SocketMock } from '../mocks';

describe('SocketRoom', () => {
  it('should extract roomId from event data', () => {
    // Arrange
    const client = new SocketMock();
    const eventData = { roomId: 'room-123' };
    
    const context = {
      switchToWs: () => ({
        getClient: () => client,
        getData: () => eventData,
      }),
    } as ExecutionContext;
    
    // Act
    const factory = SocketRoom(null, context);
    
    // Assert
    expect(factory).toBe('room-123');
  });

  it('should return null when roomId is not set', () => {
    // Arrange
    const client = new SocketMock();
    const eventData = { message: 'test' };
    
    const context = {
      switchToWs: () => ({
        getClient: () => client,
        getData: () => eventData,
      }),
    } as ExecutionContext;
    
    // Act
    const factory = SocketRoom(null, context);
    
    // Assert
    expect(factory).toBeNull();
  });
});
