import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UserRepository } from '../repositories/user.repository';
import { PermissionRepository } from '../repositories/permission.repository';
import { UserLoginDto } from '../dto/user-login.dto';
import { UserStatus } from '../enum/user-status.enum';
import { EncryptionService } from '@shared/services/encryption.service';
import { EmailService } from '@shared/services/email';
import { RedisService } from '@shared/services/redis.service';
import { S3Service } from '@shared/services/s3.service';
import { CdnService } from '@shared/services/cdn.service';
import { AppException } from '@/common/exceptions/app.exception';
import { JwtUtilService, TokenType } from '../guards/jwt.util';
import { AUTH_ERROR_CODE } from '../errors/auth-error.code';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import {
  UserLoginResponseDto,
  UserResponseDto,
} from '../dto/user-response.dto';
import {
  ChangePasswordDto,
  ChangePasswordResponseDto,
} from '../dto/change-password.dto';
import {
  ForgotPasswordDto,
  ForgotPasswordResponseDto,
  ResetPasswordDto,
  ResetPasswordResponseDto,
} from '../dto/reset-password.dto';
import {
  RegisterEmployeeDto,
  RegisterEmployeeResponseDto,
} from '../dto/register-employee.dto';
import { UpdateUserProfileDto } from '../dto/update-user-profile.dto';
import { AvatarUploadUrlResponseDto, UpdateUserAvatarDto } from '../dto/update-user-avatar.dto';
import { FileSizeEnum, TimeIntervalEnum } from '@shared/utils';
import { ImageTypeEnum } from '@shared/utils/file';

/**
 * Service xử lý authentication cho tài khoản người dùng
 */
@Injectable()
export class UserAuthService {
  private readonly logger = new Logger(UserAuthService.name);

  constructor(
    private readonly userRepository: UserRepository,
    private readonly permissionRepository: PermissionRepository,
    private readonly encryptionService: EncryptionService,
    private readonly jwtService: JwtUtilService,
    private readonly configService: ConfigService,
    private readonly emailService: EmailService,
    private readonly redisService: RedisService,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Đăng nhập tài khoản người dùng
   * @param loginDto Thông tin đăng nhập
   * @returns Token và thông tin người dùng
   */
  async login(
    loginDto: UserLoginDto,
  ): Promise<ApiResponseDto<UserLoginResponseDto>> {
    try {
      if (loginDto.recaptchaToken) {
        // Bỏ comment dòng dưới đây khi triển khai thực tế
        // const recaptchaResponse = await this.recaptchaService.verifyRecaptcha(loginDto.recaptchaToken);
        //
        // if (!recaptchaResponse.success) {
        //   throw new AppException(ErrorCode.RECAPTCHA_VERIFICATION_FAILED);
        // }
      }
    } catch (error) {
      throw new AppException(
        AUTH_ERROR_CODE.TOO_MANY_REQUESTS,
        'Xác thực reCAPTCHA thất bại',
      );
    }

    // Tìm người dùng theo username hoặc email
    const user =
      (await this.userRepository.findByUsername(loginDto.username)) ||
      (await this.userRepository.findByEmail(loginDto.username));

    if (!user) {
      throw new AppException(
        AUTH_ERROR_CODE.INVALID_CREDENTIALS,
        'Tên đăng nhập hoặc mật khẩu không chính xác',
      );
    }

    // Kiểm tra mật khẩu
    if (user.password) {
      const isPasswordValid = this.encryptionService.verifyPassword(
        loginDto.password,
        user.password,
      );

      if (!isPasswordValid) {
        throw new AppException(
          AUTH_ERROR_CODE.INVALID_CREDENTIALS,
          'Tên đăng nhập hoặc mật khẩu không chính xác',
        );
      }
    } else {
      throw new AppException(
        AUTH_ERROR_CODE.INVALID_CREDENTIALS,
        'Tài khoản không hợp lệ',
      );
    }

    // Kiểm tra trạng thái tài khoản
    if (user.status !== UserStatus.ACTIVE) {
      throw new AppException(
        AUTH_ERROR_CODE.ACCOUNT_NOT_VERIFIED,
        'Tài khoản chưa được kích hoạt hoặc đã bị khóa.',
      );
    }

    // Lấy danh sách quyền của người dùng
    const permissions = await this.permissionRepository.getUserPermissions(
      user.id,
    );

    // Tạo JWT token
    const payload = {
      id: user.id,
      sub: user.id,
      email: user.email,
      username: user.username,
      name: user.fullName,
      typeToken: TokenType.ACCESS,
      type: 'USER' as 'SYSTEM_ADMIN' | 'COMPANY_ADMIN' | 'EMPLOYEE', // Ép kiểu để phù hợp với JwtPayload
      tenantId: user.tenantId || undefined, // Đảm bảo tenantId không phải null
      permissions: permissions, // Thêm danh sách quyền vào token
    };

    // Sử dụng JwtUtilService để tạo token
    const { token: accessToken } = this.jwtService.generateToken(
      payload,
      TokenType.ACCESS,
      '24h',
    );

    // Trả về token và thông tin người dùng (không bao gồm mật khẩu)
    const { password, ...userData } = user;

    // Chuyển đổi dữ liệu entity thành DTO
    const userResponseDto = new UserResponseDto(userData as any);

    const response: UserLoginResponseDto = {
      accessToken,
      user: userResponseDto,
      permissions: permissions,
    };

    return ApiResponseDto.success(response);
  }

  /**
   * Lấy thông tin người dùng từ token
   * @param userId ID của người dùng
   * @returns Thông tin người dùng và danh sách quyền
   */
  async getUserProfile(
    userId: number,
  ): Promise<ApiResponseDto<UserLoginResponseDto>> {
    const user = await this.userRepository.findById(userId);

    if (!user) {
      throw new AppException(
        AUTH_ERROR_CODE.USER_NOT_FOUND,
        'Không tìm thấy thông tin người dùng',
      );
    }

    // Lấy danh sách quyền của người dùng
    const permissions = await this.permissionRepository.getUserPermissions(
      user.id,
    );

    // Trả về thông tin người dùng (không bao gồm mật khẩu)
    const { password, ...userData } = user;

    // Chuyển đổi dữ liệu entity thành DTO
    const userResponseDto = new UserResponseDto(userData as any);

    // Nếu có avatarUrl, tạo URL có chữ ký
    if (user.avatarUrl) {
      userResponseDto.avatarUrl = this.cdnService.generateUrlView(
        user.avatarUrl,
        TimeIntervalEnum.ONE_HOUR
      );
    }

    const response: UserLoginResponseDto = {
      accessToken: '', // Không cần trả về token mới
      user: userResponseDto,
      permissions: permissions,
    };

    return ApiResponseDto.success(response);
  }

  /**
   * Đổi mật khẩu cho người dùng đã đăng nhập
   * @param userId ID của người dùng
   * @param changePasswordDto Thông tin đổi mật khẩu
   * @returns Thông báo đổi mật khẩu thành công
   */
  async changePassword(
    userId: number,
    changePasswordDto: ChangePasswordDto,
  ): Promise<ApiResponseDto<ChangePasswordResponseDto>> {
    // Kiểm tra mật khẩu mới và xác nhận mật khẩu
    if (changePasswordDto.newPassword !== changePasswordDto.confirmPassword) {
      throw new AppException(
        AUTH_ERROR_CODE.PASSWORD_MISMATCH,
        'Mật khẩu mới và xác nhận mật khẩu không khớp',
      );
    }

    // Tìm người dùng theo ID
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new AppException(
        AUTH_ERROR_CODE.USER_NOT_FOUND,
        'Không tìm thấy thông tin người dùng',
      );
    }

    // Kiểm tra mật khẩu hiện tại
    if (!user.password) {
      throw new AppException(
        AUTH_ERROR_CODE.INVALID_CREDENTIALS,
        'Tài khoản không hợp lệ',
      );
    }

    const isPasswordValid = this.encryptionService.verifyPassword(
      changePasswordDto.currentPassword,
      user.password,
    );

    if (!isPasswordValid) {
      throw new AppException(
        AUTH_ERROR_CODE.INVALID_CURRENT_PASSWORD,
        'Mật khẩu hiện tại không chính xác',
      );
    }

    // Mã hóa mật khẩu mới
    const hashedPassword = this.encryptionService.hashPassword(
      changePasswordDto.newPassword,
    );

    // Cập nhật mật khẩu mới
    await this.userRepository.update(userId, {
      password: hashedPassword,
    });

    const response: ChangePasswordResponseDto = {
      message: 'Đổi mật khẩu thành công',
    };

    return ApiResponseDto.success(response);
  }

  /**
   * Gửi email đặt lại mật khẩu
   * @param forgotPasswordDto Thông tin quên mật khẩu
   * @returns Thông báo gửi email thành công
   */
  async forgotPassword(
    forgotPasswordDto: ForgotPasswordDto,
  ): Promise<ApiResponseDto<ForgotPasswordResponseDto>> {
    // Tìm người dùng theo email
    const user = await this.userRepository.findByEmail(forgotPasswordDto.email);
    if (!user) {
      // Không thông báo lỗi cụ thể để tránh lộ thông tin
      const response: ForgotPasswordResponseDto = {
        message:
          'Nếu email tồn tại trong hệ thống, bạn sẽ nhận được email đặt lại mật khẩu.',
      };
      return ApiResponseDto.success(response);
    }

    // Tạo token đặt lại mật khẩu
    const resetToken = this.encryptionService.generateRandomToken();

    // Lưu token vào Redis với thời hạn 1 giờ
    const resetKey = `password_reset:${resetToken}`;
    await this.redisService.setWithExpiry(
      resetKey,
      user.id.toString(),
      60 * 60, // 1 giờ
    );

    // Tạo URL đặt lại mật khẩu
    const resetUrl = `${this.configService.get<string>(
      'APP_URL',
      'http://localhost:3000',
    )}/reset-password?token=${resetToken}`;

    // Tạo nội dung email
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Đặt lại mật khẩu</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
          }
          .header {
            background-color: #f8f9fa;
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #e0e0e0;
          }
          .content {
            padding: 20px;
          }
          .button {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
          }
          .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #777;
          }
          .url-display {
            word-break: break-all;
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin: 15px 0;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>Đặt lại mật khẩu</h2>
          </div>
          <div class="content">
            <p>Xin chào,</p>
            <p>Chúng tôi nhận được yêu cầu đặt lại mật khẩu cho tài khoản của bạn. Vui lòng nhấp vào nút bên dưới để đặt lại mật khẩu:</p>
            <div style="text-align: center; margin: 25px 0;">
              <a href="${resetUrl}" class="button">Đặt lại mật khẩu</a>
            </div>
            <p>Hoặc bạn có thể sao chép và dán liên kết sau vào trình duyệt của bạn:</p>
            <div class="url-display">
              ${resetUrl}
            </div>
            <p>Liên kết này sẽ hết hạn sau 1 giờ.</p>
            <p>Nếu bạn không yêu cầu đặt lại mật khẩu, vui lòng bỏ qua email này.</p>
            <p>Trân trọng,<br>Đội ngũ hỗ trợ</p>
          </div>
          <div class="footer">
            <p>© ${new Date().getFullYear()} RedAI. Tất cả các quyền được bảo lưu.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    // Gửi email
    try {
      await this.emailService.sendEmail({
        to: user.email,
        subject: 'Đặt lại mật khẩu',
        html,
      });
    } catch (error) {
      this.logger.error(
        `Failed to send reset password email: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AUTH_ERROR_CODE.EMAIL_SENDING_ERROR,
        'Không thể gửi email đặt lại mật khẩu. Vui lòng thử lại sau.',
      );
    }

    const response: ForgotPasswordResponseDto = {
      message:
        'Email đặt lại mật khẩu đã được gửi. Vui lòng kiểm tra hộp thư của bạn.',
    };

    return ApiResponseDto.success(response);
  }

  /**
   * Đặt lại mật khẩu
   * @param resetPasswordDto Thông tin đặt lại mật khẩu
   * @returns Thông báo đặt lại mật khẩu thành công
   */
  async resetPassword(
    resetPasswordDto: ResetPasswordDto,
  ): Promise<ApiResponseDto<ResetPasswordResponseDto>> {
    // Kiểm tra mật khẩu mới và xác nhận mật khẩu
    if (resetPasswordDto.newPassword !== resetPasswordDto.confirmPassword) {
      throw new AppException(
        AUTH_ERROR_CODE.PASSWORD_MISMATCH,
        'Mật khẩu mới và xác nhận mật khẩu không khớp',
      );
    }

    // Kiểm tra token trong Redis
    const resetKey = `password_reset:${resetPasswordDto.token}`;
    const userId = await this.redisService.get(resetKey);

    if (!userId) {
      throw new AppException(
        AUTH_ERROR_CODE.TOKEN_INVALID_OR_EXPIRED,
        'Token đặt lại mật khẩu không hợp lệ hoặc đã hết hạn',
      );
    }

    // Tìm người dùng theo ID
    const user = await this.userRepository.findById(Number(userId));
    if (!user) {
      throw new AppException(
        AUTH_ERROR_CODE.USER_NOT_FOUND,
        'Không tìm thấy thông tin người dùng',
      );
    }

    // Mã hóa mật khẩu mới
    const hashedPassword = this.encryptionService.hashPassword(
      resetPasswordDto.newPassword,
    );

    // Cập nhật mật khẩu mới
    await this.userRepository.update(user.id, {
      password: hashedPassword,
    });

    // Xóa token khỏi Redis
    await this.redisService.del(resetKey);

    const response: ResetPasswordResponseDto = {
      message:
        'Đặt lại mật khẩu thành công. Bạn có thể đăng nhập bằng mật khẩu mới.',
    };

    return ApiResponseDto.success(response);
  }

  /**
   * Đăng ký tài khoản nhân viên mới
   * @param registerDto Thông tin đăng ký
   * @returns Thông báo đăng ký thành công
   */
  async registerEmployee(
    registerDto: RegisterEmployeeDto,
  ): Promise<ApiResponseDto<RegisterEmployeeResponseDto>> {
    try {
      // Kiểm tra username đã tồn tại chưa
      const existingUsername = await this.userRepository.findByUsername(
        registerDto.username,
      );
      if (existingUsername) {
        throw new AppException(
          AUTH_ERROR_CODE.USERNAME_ALREADY_EXISTS,
          'Tên đăng nhập đã được sử dụng bởi một tài khoản khác',
        );
      }

      // Kiểm tra email đã tồn tại chưa
      const existingEmail = await this.userRepository.findByEmail(
        registerDto.email,
      );
      if (existingEmail) {
        throw new AppException(
          AUTH_ERROR_CODE.EMAIL_ALREADY_EXISTS,
          'Email đã được sử dụng bởi một tài khoản khác',
        );
      }

      // Mã hóa mật khẩu
      const hashedPassword = this.encryptionService.hashPassword(
        registerDto.password,
      );

      // Tạo ID cho người dùng mới
      const id = Date.now();

      // Tạo tài khoản người dùng mới với trạng thái ACTIVE
      await this.userRepository.create({
        id,
        username: registerDto.username,
        email: registerDto.email,
        password: hashedPassword,
        fullName: registerDto.fullName,
        departmentId: registerDto.departmentId || null,
        status: UserStatus.ACTIVE, // Tài khoản nhân viên được kích hoạt ngay lập tức
        createdAt: Date.now(),
        // tenantId được tự động thêm bởi middleware bảo mật tenant
      });

      // Trả về thông báo đăng ký thành công
      const response: RegisterEmployeeResponseDto = {
        message: 'Đăng ký tài khoản nhân viên thành công',
      };

      return ApiResponseDto.created(response);
    } catch (error) {
      // Xử lý lỗi
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Lỗi khi đăng ký tài khoản nhân viên: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AUTH_ERROR_CODE.REGISTRATION_FAILED,
        'Đăng ký tài khoản nhân viên thất bại: ' + error.message,
      );
    }
  }

  /**
   * Cập nhật thông tin cá nhân của người dùng
   * @param userId ID của người dùng
   * @param updateProfileDto Thông tin cần cập nhật
   * @returns Thông tin người dùng đã cập nhật
   */
  async updateUserProfile(
    userId: number,
    updateProfileDto: UpdateUserProfileDto,
  ): Promise<ApiResponseDto<UserResponseDto>> {
    try {
      // Tìm người dùng theo ID
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new AppException(
          AUTH_ERROR_CODE.USER_NOT_FOUND,
          'Không tìm thấy thông tin người dùng',
        );
      }

      // Cập nhật thông tin người dùng
      const updatedUser = await this.userRepository.update(userId, updateProfileDto);

      // Trả về thông tin người dùng đã cập nhật (không bao gồm mật khẩu)
      const { password, ...userData } = updatedUser;

      // Chuyển đổi dữ liệu entity thành DTO
      const userResponseDto = new UserResponseDto(userData as any);

      // Nếu có avatarUrl, tạo URL có chữ ký
      if (updatedUser.avatarUrl) {
        userResponseDto.avatarUrl = this.cdnService.generateUrlView(
          updatedUser.avatarUrl,
          TimeIntervalEnum.ONE_HOUR
        );
      }

      return ApiResponseDto.success(userResponseDto, 'Cập nhật thông tin cá nhân thành công');
    } catch (error) {
      this.logger.error(
        `Error updating user profile: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AUTH_ERROR_CODE.USER_NOT_FOUND,
        'Cập nhật thông tin cá nhân thất bại',
      );
    }
  }

  /**
   * Tạo URL tạm thời để upload avatar
   * @param userId ID của người dùng
   * @returns URL tạm thời và key
   */
  async createAvatarUploadUrl(
    userId: number,
  ): Promise<ApiResponseDto<AvatarUploadUrlResponseDto>> {
    try {
      // Tìm người dùng theo ID
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new AppException(
          AUTH_ERROR_CODE.USER_NOT_FOUND,
          'Không tìm thấy thông tin người dùng',
        );
      }

      // Tạo key cho file (đường dẫn trên S3/Cloudflare R2)
      const key = `users/${userId}/avatar/${Date.now()}.jpg`;

      // Tạo URL tạm thời có chữ ký số với thời hạn 15 phút
      const uploadUrl = await this.s3Service.createPresignedWithID(
        key,
        TimeIntervalEnum.FIFTEEN_MINUTES,
        ImageTypeEnum.JPEG,
        FileSizeEnum.ONE_MB
      );

      const response: AvatarUploadUrlResponseDto = {
        uploadUrl,
        key,
      };

      return ApiResponseDto.success(response, 'Tạo URL upload avatar thành công');
    } catch (error) {
      this.logger.error(
        `Error creating avatar upload URL: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AUTH_ERROR_CODE.USER_NOT_FOUND,
        'Tạo URL upload avatar thất bại',
      );
    }
  }

  /**
   * Cập nhật avatar của người dùng
   * @param userId ID của người dùng
   * @param updateAvatarDto Thông tin avatar mới
   * @returns Thông tin người dùng đã cập nhật
   */
  async updateUserAvatar(
    userId: number,
    updateAvatarDto: UpdateUserAvatarDto,
  ): Promise<ApiResponseDto<UserResponseDto>> {
    try {
      // Tìm người dùng theo ID
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new AppException(
          AUTH_ERROR_CODE.USER_NOT_FOUND,
          'Không tìm thấy thông tin người dùng',
        );
      }

      // Cập nhật avatarUrl
      const updatedUser = await this.userRepository.update(userId, {
        avatarUrl: updateAvatarDto.avatarKey,
      });

      // Trả về thông tin người dùng đã cập nhật (không bao gồm mật khẩu)
      const { password, ...userData } = updatedUser;

      // Chuyển đổi dữ liệu entity thành DTO
      const userResponseDto = new UserResponseDto(userData as any);

      // Tạo URL có chữ ký cho avatar mới
      if (updatedUser.avatarUrl) {
        userResponseDto.avatarUrl = this.cdnService.generateUrlView(
          updatedUser.avatarUrl,
          TimeIntervalEnum.ONE_HOUR
        );
      }

      return ApiResponseDto.success(userResponseDto, 'Cập nhật avatar thành công');
    } catch (error) {
      this.logger.error(
        `Error updating user avatar: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AUTH_ERROR_CODE.USER_NOT_FOUND,
        'Cập nhật avatar thất bại',
      );
    }
  }
}
